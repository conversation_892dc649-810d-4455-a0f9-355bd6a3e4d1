#!/usr/bin/env bash
set -e

############ 1. 环境变量检查 ############
echo "检查必要环境变量..."

# 数据库配置检查
if [ -z "$DB_HOST" ] || [ -z "$DB_DATABASE" ] || [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ]; then
    echo "错误: 缺少数据库配置环境变量"
    echo "必需变量: DB_HOST, DB_DATABASE, DB_USERNAME, DB_PASSWORD"
    exit 1
fi

# Redis 配置检查
if [ -z "$REDIS_HOST" ]; then
    echo "错误: 缺少 REDIS_HOST 环境变量"
    exit 1
fi

############ 2. 数据库配置 ##########
export DATABASE_TYPE=postgresql
export DB_USERNAME=${DB_USERNAME:-}
export DB_PASSWORD=${DB_PASSWORD:-}
export DB_HOST=${DB_HOST:-}
export DB_PORT=${DB_PORT:-26257}
export DB_DATABASE=${DB_DATABASE:-}

# 设置数据库连接池配置
export SQLALCHEMY_POOL_SIZE=${SQLALCHEMY_POOL_SIZE:-30}
export SQLALCHEMY_POOL_RECYCLE=${SQLALCHEMY_POOL_RECYCLE:-3600}
export SQLALCHEMY_ECHO=${SQLALCHEMY_ECHO:-false}

export SQLALCHEMY_DATABASE_URI="postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}"
echo "SQLALCHEMY_DATABASE_URI 已设置"

############ 3. Redis 配置 ##########
export REDIS_PORT=${REDIS_PORT:-6379}
export REDIS_DB=${REDIS_DB:-0}
export REDIS_USERNAME=${REDIS_USERNAME:-}
export REDIS_PASSWORD=${REDIS_PASSWORD:-}
export REDIS_USE_SSL=${REDIS_USE_SSL:-false}

# 支持 Redis Sentinel 配置
export REDIS_USE_SENTINEL=${REDIS_USE_SENTINEL:-false}
export REDIS_SENTINELS=${REDIS_SENTINELS:-}
export REDIS_SENTINEL_SERVICE_NAME=${REDIS_SENTINEL_SERVICE_NAME:-}
export REDIS_SENTINEL_USERNAME=${REDIS_SENTINEL_USERNAME:-}
export REDIS_SENTINEL_PASSWORD=${REDIS_SENTINEL_PASSWORD:-}
export REDIS_SENTINEL_SOCKET_TIMEOUT=${REDIS_SENTINEL_SOCKET_TIMEOUT:-0.1}

echo "Redis 连接: ${REDIS_HOST}:${REDIS_PORT} (DB: ${REDIS_DB})"

export OPENDAL_SCHEME=fs
export OPENDAL_FS_ROOT=/tmp/storage

############ 4. 连接测试 ##########
echo "测试数据库连接..."
cd /app/api

# 测试数据库连接
python3 -c "
import psycopg2
import sys
import os

try:
    conn_params = {
        'host': '${DB_HOST}',
        'port': ${DB_PORT},
        'database': '${DB_DATABASE}',
        'user': '${DB_USERNAME}',
        'password': '${DB_PASSWORD}'
    }

    if os.getenv('DB_SSL_CA_CERT'):
        conn_params['sslrootcert'] = os.getenv('DB_SSL_CA_CERT')
    if os.getenv('DB_SSL_CLIENT_CERT'):
        conn_params['sslcert'] = os.getenv('DB_SSL_CLIENT_CERT')
    if os.getenv('DB_SSL_CLIENT_KEY'):
        conn_params['sslkey'] = os.getenv('DB_SSL_CLIENT_KEY')

    conn = psycopg2.connect(**conn_params)

    with conn.cursor() as cur:
        cur.execute('SELECT version()')
        version = cur.fetchone()[0]
        print(f'数据库连接成功: {version.split()[0]} {version.split()[1]}')

    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
    sys.exit(1)
"

# 测试 Redis 连接
python3 -c "
import redis
import sys
try:
    redis_kwargs = {
        'host': '${REDIS_HOST}',
        'port': ${REDIS_PORT},
        'db': ${REDIS_DB},
        'ssl': ${REDIS_USE_SSL}
    }

    if '${REDIS_USERNAME}':
        redis_kwargs['username'] = '${REDIS_USERNAME}'
    if '${REDIS_PASSWORD}':
        redis_kwargs['password'] = '${REDIS_PASSWORD}'

    r = redis.Redis(**redis_kwargs)
    r.ping()
    print('Redis 连接成功')
except Exception as e:
    print(f'Redis 连接失败: {e}')
    sys.exit(1)
"

############ 5. 数据库迁移 #############
echo "执行数据库迁移..."
FLASK_APP=app.py flask db upgrade || echo "迁移完成或跳过"

############ 6. 准备 Nginx 临时目录 ############
echo "准备 Nginx 临时目录..."
mkdir -p /tmp/nginx_client_temp \
         /tmp/nginx_proxy_temp \
         /tmp/nginx_fastcgi_temp \
         /tmp/nginx_uwsgi_temp \
         /tmp/nginx_scgi_temp \
         /tmp/nginx_logs

# 确保目录权限正确
chmod 755 /tmp/nginx_*

############ 7. 启动后端 ############
echo "启动后端 API..."
cd /app/api

# 检查必要的 Python 模块
python3 -c "import flask, gunicorn, gevent" || {
    echo "缺少必要的 Python 模块"
    exit 1
}

# 使用更保守的 gunicorn 配置
gunicorn app:app \
    --bind 127.0.0.1:5001 \
    --workers 1 \
    --worker-class gevent \
    --worker-connections 500 \
    --timeout 120 \
    --max-requests 1000 \
    --max-requests-jitter 50 \
    --log-level info \
    --access-logfile - \
    --error-logfile - \
    --capture-output &

BACKEND_PID=$!

# 等待后端启动
echo "等待后端启动..."
for i in {1..30}; do
    if curl -f -s http://127.0.0.1:5001/health > /dev/null 2>&1; then
        echo "✓ 后端启动成功 (PID: $BACKEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "✗ 后端启动超时"
        exit 1
    fi
    sleep 2
done

############ 8. 启动前端 ############
echo "启动前端服务..."
cd /app/web

# 设置前端环境变量，让前端通过相对路径访问 API
export NEXT_PUBLIC_API_PREFIX=""
export NEXT_PUBLIC_PUBLIC_API_PREFIX=""
export NEXT_PUBLIC_SENTRY_DSN=""
export NEXT_PUBLIC_SITE_ABOUT=""
export NEXT_PUBLIC_EDITION="SELF_HOSTED"

# 尝试在运行时覆盖 API 配置（如果前端支持）
if [ -f ".env.local" ]; then
    rm .env.local
fi

cat > .env.local << EOF
NEXT_PUBLIC_API_PREFIX=
NEXT_PUBLIC_PUBLIC_API_PREFIX=
EOF

# 创建临时的 next.config.js 来覆盖重定向配置
echo "创建临时 Next.js 配置..."
cat > next.config.temp.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  trailingSlash: false,
  // 移除可能有问题的重定向
  async redirects() {
    return []
  },
  // 添加重写规则，确保根路径能正常工作
  async rewrites() {
    return [
      {
        source: '/',
        destination: '/apps'
      }
    ]
  }
}

module.exports = nextConfig
EOF

# 备份原配置并使用新配置
if [ -f "next.config.js" ]; then
    mv next.config.js next.config.js.backup
fi
mv next.config.temp.js next.config.js

# 检查是否有配置文件可以修改
echo "检查前端配置文件..."
find . -name "*.js" -o -name "*.json" | head -5
ls -la .env* 2>/dev/null || echo "没有找到 .env 文件"

# 前端绑定到内部端口
HOSTNAME=127.0.0.1 PORT=3000 NODE_ENV=production node server.js &
FRONTEND_PID=$!

# 等待前端启动
echo "等待前端启动..."
for i in {1..30}; do
    # 测试前端服务
    FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000 2>/dev/null)

    if [ "$FRONTEND_STATUS" = "200" ] || [ "$FRONTEND_STATUS" = "301" ] || [ "$FRONTEND_STATUS" = "302" ]; then
        echo "✓ 前端启动成功 (PID: $FRONTEND_PID, HTTP: $FRONTEND_STATUS)"

        # 额外测试 /apps 路径
        APPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000/apps 2>/dev/null)
        echo "  /apps 路径状态: $APPS_STATUS"

        # 测试根路径重定向
        ROOT_LOCATION=$(curl -s -I http://127.0.0.1:3000 2>/dev/null | grep -i "location:" | cut -d' ' -f2 | tr -d '\r\n')
        if [ ! -z "$ROOT_LOCATION" ]; then
            echo "  根路径重定向到: $ROOT_LOCATION"
        fi

        break
    fi

    if [ $i -eq 30 ]; then
        echo "✗ 前端启动超时，最后状态码: $FRONTEND_STATUS"
        # 显示前端进程状态
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "  前端进程仍在运行，可能是应用层问题"
        else
            echo "  前端进程已退出"
        fi
        exit 1
    fi
    sleep 2
done

############ 9. 测试 Nginx 配置 ############
echo "测试 Nginx 配置..."
if nginx -t; then
    echo "✓ Nginx 配置测试通过"
else
    echo "✗ Nginx 配置测试失败"
    exit 1
fi

############ 10. 启动 Nginx ############
echo "启动 Nginx..."
nginx &
NGINX_PID=$!

# 等待 Nginx 启动
echo "等待 Nginx 启动..."
sleep 3
echo "✓ Nginx 启动完成 (PID: $NGINX_PID)"

echo "=========================================="
echo "🎉 所有服务启动完成！"
echo "服务访问地址: http://localhost:7860"
echo "进程 PIDs: Nginx=$NGINX_PID, 前端=$FRONTEND_PID, 后端=$BACKEND_PID"
echo "=========================================="

# 启动后诊断
echo "🔍 启动后诊断..."
sleep 2

# 测试各个服务
echo "后端健康检查: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5001/health 2>/dev/null)"
echo "前端根路径: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000 2>/dev/null)"
echo "前端 /apps: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000/apps 2>/dev/null)"
echo "Nginx 根路径: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:7860 2>/dev/null)"
echo "Nginx 健康检查: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:7860/nginx-health 2>/dev/null)"

# 显示调试链接
echo "🔧 调试链接:"
echo "  - 前端调试: http://localhost:7860/debug-frontend"
echo "  - 后端调试: http://localhost:7860/debug-backend"
echo "=========================================="

# 创建监控函数
monitor_services() {
    while true; do
        sleep 30
        # 检查关键进程是否还在运行
        if ! kill -0 $NGINX_PID 2>/dev/null; then
            echo "❌ Nginx 进程已停止"
            exit 1
        fi
        if ! kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "❌ 前端进程已停止"
            exit 1
        fi
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            echo "❌ 后端进程已停止"
            exit 1
        fi
    done
}

# 优雅关闭处理
cleanup() {
    echo "正在关闭服务..."
    kill $NGINX_PID $FRONTEND_PID $BACKEND_PID 2>/dev/null || true
    wait
    echo "服务已关闭"
}

trap cleanup SIGTERM SIGINT

# 启动监控并等待
monitor_services &
wait