#!/bin/bash

echo "=== Dify 服务诊断脚本 ==="
echo

# 1. 测试后端服务
echo "1. 测试后端服务 (127.0.0.1:5001):"
if curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5001/health | grep -q "200"; then
    echo "   ✓ 后端健康检查通过"
    echo "   响应内容: $(curl -s http://127.0.0.1:5001/health)"
else
    echo "   ✗ 后端健康检查失败"
fi
echo

# 2. 测试前端服务
echo "2. 测试前端服务 (127.0.0.1:3000):"
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000 2>/dev/null)
echo "   HTTP 状态码: $FRONTEND_STATUS"

if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "   ✓ 前端服务响应正常"
elif [ "$FRONTEND_STATUS" = "301" ] || [ "$FRONTEND_STATUS" = "302" ]; then
    echo "   ⚠ 前端服务返回重定向"
    echo "   Location 头: $(curl -s -I http://127.0.0.1:3000 | grep -i location)"
elif [ "$FRONTEND_STATUS" = "404" ]; then
    echo "   ✗ 前端服务返回 404"
else
    echo "   ✗ 前端服务异常，状态码: $FRONTEND_STATUS"
fi

# 测试前端的 /apps 路径
echo "   测试 /apps 路径:"
APPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000/apps 2>/dev/null)
echo "   /apps HTTP 状态码: $APPS_STATUS"
echo

# 3. 测试 Nginx
echo "3. 测试 Nginx (127.0.0.1:7860):"
NGINX_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:7860 2>/dev/null)
echo "   HTTP 状态码: $NGINX_STATUS"

if [ "$NGINX_STATUS" = "200" ]; then
    echo "   ✓ Nginx 代理正常"
elif [ "$NGINX_STATUS" = "301" ] || [ "$NGINX_STATUS" = "302" ]; then
    echo "   ⚠ Nginx 返回重定向"
    echo "   Location 头: $(curl -s -I http://127.0.0.1:7860 | grep -i location)"
elif [ "$NGINX_STATUS" = "404" ]; then
    echo "   ✗ Nginx 返回 404"
elif [ "$NGINX_STATUS" = "502" ]; then
    echo "   ✗ Nginx 返回 502 (后端不可达)"
else
    echo "   ✗ Nginx 异常，状态码: $NGINX_STATUS"
fi

# 测试 Nginx 健康检查
echo "   测试 Nginx 健康检查:"
NGINX_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:7860/nginx-health 2>/dev/null)
echo "   /nginx-health 状态码: $NGINX_HEALTH"
echo

# 4. 检查进程状态
echo "4. 检查进程状态:"
echo "   Nginx 进程:"
ps aux | grep nginx | grep -v grep || echo "   未找到 nginx 进程"
echo "   Node.js 进程:"
ps aux | grep node | grep -v grep || echo "   未找到 node 进程"
echo "   Gunicorn 进程:"
ps aux | grep gunicorn | grep -v grep || echo "   未找到 gunicorn 进程"
echo

# 5. 检查端口监听
echo "5. 检查端口监听:"
echo "   端口 3000 (前端):"
netstat -tlnp 2>/dev/null | grep :3000 || echo "   端口 3000 未监听"
echo "   端口 5001 (后端):"
netstat -tlnp 2>/dev/null | grep :5001 || echo "   端口 5001 未监听"
echo "   端口 7860 (Nginx):"
netstat -tlnp 2>/dev/null | grep :7860 || echo "   端口 7860 未监听"
echo

# 6. 检查 Nginx 错误日志
echo "6. Nginx 错误日志 (最近10行):"
if [ -f "/tmp/nginx_logs/error.log" ]; then
    tail -10 /tmp/nginx_logs/error.log
else
    echo "   错误日志文件不存在"
fi
echo

echo "=== 诊断完成 ==="
