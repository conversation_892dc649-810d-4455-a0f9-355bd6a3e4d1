const translation = {
  title: '<PERSON>ông cụ',
  createCustomTool: 'Tạo công cụ tùy chỉnh',
  type: {
    all: 'Tất cả',
    builtIn: 'Tích hợp sẵn',
    custom: 'Tùy chỉnh',
    workflow: 'Quy trình làm việc',
  },
  contribute: {
    line1: 'Tôi quan tâm đến việc ',
    line2: 'đóng góp công cụ cho Dify.',
    viewGuide: 'Xem hướng dẫn',
  },
  author: 'Tác giả',
  auth: {
    authorized: 'Đã xác thực',
    setup: 'Thiết lập xác thực để sử dụng',
    setupModalTitle: 'Thiết lập xác thực',
    setupModalTitleDescription: 'Sau khi cấu hình thông tin đăng nhập, tất cả thành viên trong không gian làm việc có thể sử dụng công cụ này khi triển khai ứng dụng.',
  },
  includeToolNum: '<PERSON>o gồm {{num}} công cụ',
  addTool: 'Thêm công cụ',
  createTool: {
    title: 'Tạo công cụ tùy chỉnh',
    editAction: 'Cấu hình',
    editTitle: 'Chỉnh sửa công cụ tùy chỉnh',
    name: 'Tên',
    toolNamePlaceHolder: 'Nhập tên công cụ',
    schema: 'Schema',
    schemaPlaceHolder: 'Nhập schema OpenAPI của bạn vào đây',
    viewSchemaSpec: 'Xem chi tiết OpenAPI-Swagger',
    importFromUrl: 'Nhập từ URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Vui lòng nhập URL hợp lệ',
    examples: 'Ví dụ',
    exampleOptions: {
      json: 'Thời tiết (JSON)',
      yaml: 'Cửa hàng thú cưng (YAML)',
      blankTemplate: 'Mẫu trống',
    },
    availableTools: {
      title: 'Công cụ hiện có',
      name: 'Tên',
      description: 'Mô tả',
      method: 'Phương thức',
      path: 'Đường dẫn',
      action: 'Hành động',
      test: 'Kiểm tra',
    },
    authMethod: {
      title: 'Phương thức xác thực',
      type: 'Loại xác thực',
      keyTooltip: 'Khóa tiêu đề HTTP, bạn có thể để trống nếu không biết hoặc đặt một giá trị tùy chỉnh',
      types: {
        none: 'Không',
        api_key: 'Khóa API',
        apiKeyPlaceholder: 'Tên tiêu đề HTTP cho Khóa API',
        apiValuePlaceholder: 'Nhập Khóa API',
        api_key_query: 'Tham số truy vấn',
        api_key_header: 'Tiêu đề',
        queryParamPlaceholder: 'Tên tham số truy vấn cho khóa API',
      },
      key: 'Khóa',
      value: 'Giá trị',
      queryParam: 'Tham số truy vấn',
      queryParamTooltip: 'Tên tham số truy vấn của API key để truyền vào, ví dụ: "key" trong "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Loại xác thực',
      types: {
        basic: 'Cơ bản',
        bearer: 'Bearer',
        custom: 'Tùy chỉnh',
      },
    },
    privacyPolicy: 'Chính sách bảo mật',
    privacyPolicyPlaceholder: 'Vui lòng nhập chính sách bảo mật',
    customDisclaimer: 'Tuyên bố từ chối trách nhiệm tùy chỉnh',
    customDisclaimerPlaceholder: 'Vui lòng nhập tuyên bố từ chối trách nhiệm tùy chỉnh',
    deleteToolConfirmTitle: 'Xóa công cụ này?',
    deleteToolConfirmContent: 'Xóa công cụ là không thể hoàn tác. Người dùng sẽ không thể truy cập lại công cụ của bạn.',
    toolInput: {
      label: 'Tags',
      methodParameter: 'Thông số',
      name: 'Tên',
      descriptionPlaceholder: 'Mô tả ý nghĩa của tham số',
      methodSetting: 'Khung cảnh',
      title: 'Công cụ nhập liệu',
      methodSettingTip: 'Người dùng điền vào cấu hình công cụ',
      required: 'Bắt buộc',
      method: 'Phương pháp',
      methodParameterTip: 'LLM lấp đầy trong quá trình suy luận',
      description: 'Sự miêu tả',
      labelPlaceholder: 'Chọn thẻ (tùy chọn)',
    },
    nameForToolCallTip: 'Chỉ hỗ trợ số, chữ cái và dấu gạch dưới.',
    nameForToolCall: 'Công cụ gọi tên',
    nameForToolCallPlaceHolder: 'Được sử dụng để nhận dạng máy, chẳng hạn như getCurrentWeather, list_pets',
    descriptionPlaceholder: 'Mô tả ngắn gọn về mục đích của công cụ, ví dụ: lấy nhiệt độ cho một vị trí cụ thể.',
    description: 'Sự miêu tả',
    confirmTitle: 'Xác nhận để lưu ?',
    confirmTip: 'Các ứng dụng sử dụng công cụ này sẽ bị ảnh hưởng',
  },
  test: {
    title: 'Kiểm tra',
    parametersValue: 'Tham số & Giá trị',
    parameters: 'Tham số',
    value: 'Giá trị',
    testResult: 'Kết quả kiểm tra',
    testResultPlaceholder: 'Kết quả kiểm tra sẽ hiển thị ở đây',
  },
  thought: {
    using: 'Đang sử dụng',
    used: 'Đã sử dụng',
    requestTitle: 'Yêu cầu đến',
    responseTitle: 'Phản hồi từ',
  },
  setBuiltInTools: {
    info: 'Thông tin',
    setting: 'Cài đặt',
    toolDescription: 'Mô tả công cụ',
    parameters: 'Tham số',
    string: 'chuỗi',
    number: 'số',
    required: 'Bắt buộc',
    infoAndSetting: 'Thông tin & Cài đặt',
    file: 'tệp',
  },
  noCustomTool: {
    title: 'Chưa có công cụ tùy chỉnh!',
    content: 'Thêm và quản lý các công cụ tùy chỉnh của bạn ở đây để xây dựng ứng dụng AI.',
    createTool: 'Tạo công cụ',
  },
  noSearchRes: {
    title: 'Xin lỗi, không có kết quả!',
    content: 'Chúng tôi không tìm thấy công cụ nào phù hợp với tìm kiếm của bạn.',
    reset: 'Đặt lại tìm kiếm',
  },
  builtInPromptTitle: 'Lời nhắc',
  toolRemoved: 'Công cụ đã bị xóa',
  notAuthorized: 'Công cụ chưa được xác thực',
  howToGet: 'Cách nhận',
  addToolModal: {
    category: 'loại',
    manageInTools: 'Quản lý trong Công cụ',
    type: 'kiểu',
    add: 'thêm',
    added: 'Thêm',
    custom: {
      title: 'Không có công cụ tùy chỉnh nào',
      tip: 'Tạo một công cụ tùy chỉnh',
    },
    workflow: {
      title: 'Không có công cụ quy trình nào',
      tip: 'Xuất bản các quy trình dưới dạng công cụ trong Studio',
    },
    mcp: {
      title: 'Không có công cụ MCP nào',
      tip: 'Thêm máy chủ MCP',
    },
    agent: {
      title: 'Không có chiến lược đại lý nào',
    },
  },
  toolNameUsageTip: 'Tên cuộc gọi công cụ để lý luận và nhắc nhở tổng đài viên',
  customToolTip: 'Tìm hiểu thêm về các công cụ tùy chỉnh Dify',
  openInStudio: 'Mở trong Studio',
  noTools: 'Không tìm thấy công cụ',
  copyToolName: 'Sao chép tên',
  mcp: {
    create: {
      cardTitle: 'Thêm Máy chủ MCP (HTTP)',
      cardLink: 'Tìm hiểu thêm về tích hợp máy chủ MCP',
    },
    noConfigured: 'Máy chủ Chưa được Cấu hình',
    updateTime: 'Cập nhật',
    toolsCount: '{count} công cụ',
    noTools: 'Không có công cụ nào',
    modal: {
      title: 'Thêm Máy chủ MCP (HTTP)',
      editTitle: 'Sửa Máy chủ MCP (HTTP)',
      name: 'Tên & Biểu tượng',
      namePlaceholder: 'Đặt tên máy chủ MCP',
      serverUrl: 'URL Máy chủ',
      serverUrlPlaceholder: 'URL đến điểm cuối máy chủ',
      serverUrlWarning: 'Cập nhật địa chỉ máy chủ có thể làm gián đoạn ứng dụng phụ thuộc vào máy chủ này',
      serverIdentifier: 'Định danh Máy chủ',
      serverIdentifierTip: 'Định danh duy nhất cho máy chủ MCP trong không gian làm việc. Chỉ chữ thường, số, gạch dưới và gạch ngang. Tối đa 24 ký tự.',
      serverIdentifierPlaceholder: 'Định danh duy nhất, VD: my-mcp-server',
      serverIdentifierWarning: 'Máy chủ sẽ không được nhận diện bởi ứng dụng hiện có sau khi thay đổi ID',
      cancel: 'Hủy',
      save: 'Lưu',
      confirm: 'Thêm & Ủy quyền',
    },
    delete: 'Xóa Máy chủ MCP',
    deleteConfirmTitle: 'Xóa {mcp}?',
    operation: {
      edit: 'Sửa',
      remove: 'Xóa',
    },
    authorize: 'Ủy quyền',
    authorizing: 'Đang ủy quyền...',
    authorizingRequired: 'Cần ủy quyền',
    authorizeTip: 'Sau khi ủy quyền, công cụ sẽ hiển thị tại đây.',
    update: 'Cập nhật',
    updating: 'Đang cập nhật...',
    gettingTools: 'Đang lấy công cụ...',
    updateTools: 'Đang cập nhật công cụ...',
    toolsEmpty: 'Công cụ chưa tải',
    getTools: 'Lấy công cụ',
    toolUpdateConfirmTitle: 'Cập nhật Danh sách Công cụ',
    toolUpdateConfirmContent: 'Cập nhật danh sách công cụ có thể ảnh hưởng ứng dụng hiện có. Tiếp tục?',
    toolsNum: 'Bao gồm {count} công cụ',
    onlyTool: 'Bao gồm 1 công cụ',
    identifier: 'Định danh Máy chủ (Nhấn để Sao chép)',
    server: {
      title: 'Máy chủ MCP',
      url: 'URL Máy chủ',
      reGen: 'Tạo lại URL máy chủ?',
      addDescription: 'Thêm mô tả',
      edit: 'Sửa mô tả',
      modal: {
        addTitle: 'Thêm mô tả để kích hoạt máy chủ MCP',
        editTitle: 'Sửa mô tả',
        description: 'Mô tả',
        descriptionPlaceholder: 'Giải thích chức năng công cụ và cách LLM sử dụng',
        parameters: 'Tham số',
        parametersTip: 'Thêm mô tả cho từng tham số để giúp LLM hiểu mục đích và ràng buộc.',
        parametersPlaceholder: 'Mục đích và ràng buộc của tham số',
        confirm: 'Kích hoạt Máy chủ MCP',
      },
      publishTip: 'Ứng dụng chưa xuất bản. Vui lòng xuất bản ứng dụng trước.',
    },
  },
}

export default translation
