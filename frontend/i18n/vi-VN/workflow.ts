const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON> tác',
    redo: '<PERSON><PERSON><PERSON> lạ<PERSON>',
    editing: '<PERSON><PERSON> chỉnh sửa',
    autoSaved: 'Đã tự động lưu',
    unpublished: 'Chưa xuất bản',
    published: '<PERSON><PERSON> xuất bản',
    publish: '<PERSON><PERSON>t bản',
    update: '<PERSON><PERSON><PERSON> nhật',
    run: 'Chạy',
    running: '<PERSON>ang chạy',
    inRunMode: 'Chế độ chạy',
    inPreview: 'Trong chế độ xem trước',
    inPreviewMode: 'Chế độ xem trước',
    preview: 'Xem trước',
    viewRunHistory: '<PERSON>em lịch sử chạy',
    runHistory: '<PERSON><PERSON><PERSON> sử chạy',
    goBackToEdit: 'Quay lại trình chỉnh sửa',
    conversationLog: 'Nhật ký cuộc trò chuyện',
    features: 'Tính năng',
    debugAndPreview: 'Xem trước',
    restart: 'Khởi động lại',
    currentDraft: '<PERSON><PERSON><PERSON> nháp hiện tại',
    currentDraftUnpublished: '<PERSON><PERSON>n nháp hiện tại chưa xuất bản',
    latestPublished: 'Xuất bản mới nhất',
    publishedAt: 'Đã xuất bản lúc',
    restore: 'Khôi phục',
    runApp: 'Chạy ứng dụng',
    batchRunApp: 'Chạy ứng dụng hàng loạt',
    accessAPIReference: 'Truy cập tài liệu API',
    embedIntoSite: 'Nhúng vào trang web',
    addTitle: 'Thêm tiêu đề...',
    addDescription: 'Thêm mô tả...',
    noVar: 'Không có biến',
    searchVar: 'Tìm kiếm biến',
    variableNamePlaceholder: 'Tên biến',
    setVarValuePlaceholder: 'Đặt giá trị biến',
    needConnectTip: 'Bước này không được kết nối với bất kỳ điều gì',
    maxTreeDepth: 'Giới hạn tối đa {{depth}} nút trên mỗi nhánh',
    workflowProcess: 'Quy trình làm việc',
    notRunning: 'Chưa chạy',
    previewPlaceholder: 'Nhập nội dung vào hộp bên dưới để bắt đầu gỡ lỗi Chatbot',
    effectVarConfirm: {
      title: 'Xóa biến',
      content: 'Biến được sử dụng trong các nút khác. Bạn có chắc chắn muốn xóa nó không?',
    },
    insertVarTip: 'Nhấn phím \'/\' để chèn nhanh',
    processData: 'Xử lý dữ liệu',
    input: 'Đầu vào',
    output: 'Đầu ra',
    jinjaEditorPlaceholder: 'Gõ \'/\' hoặc \'{\' để chèn biến',
    viewOnly: 'Chỉ xem',
    showRunHistory: 'Hiển thị lịch sử chạy',
    enableJinja: 'Bật hỗ trợ mẫu Jinja',
    learnMore: 'Tìm hiểu thêm',
    copy: 'Sao chép',
    duplicate: 'Nhân bản',
    pasteHere: 'Dán vào đây',
    pointerMode: 'Chế độ con trỏ',
    handMode: 'Chế độ tay',
    model: 'Mô hình',
    workflowAsTool: 'Quy trình làm việc như công cụ',
    configureRequired: 'Yêu cầu cấu hình',
    configure: 'Cấu hình',
    manageInTools: 'Quản lý trong công cụ',
    workflowAsToolTip: 'Cần cấu hình lại công cụ sau khi cập nhật quy trình làm việc.',
    viewDetailInTracingPanel: 'Xem chi tiết',
    importSuccess: 'Nhập thành công',
    backupCurrentDraft: 'Sao lưu dự thảo hiện tại',
    chooseDSL: 'Chọn tệp DSL(yml)',
    importDSLTip: 'Dự thảo hiện tại sẽ bị ghi đè. Xuất quy trình làm việc dưới dạng bản sao lưu trước khi nhập.',
    importFailure: 'Nhập không thành công',
    overwriteAndImport: 'Ghi đè và nhập',
    importDSL: 'Nhập DSL',
    syncingData: 'Đồng bộ hóa dữ liệu, chỉ vài giây.',
    parallelTip: {
      click: {
        title: 'Bấm',
        desc: 'để thêm',
      },
      drag: {
        title: 'Kéo',
        desc: 'Để kết nối',
      },
      limit: 'Song song được giới hạn trong các nhánh {{num}}.',
      depthLimit: 'Giới hạn lớp lồng song song của {{num}} layer',
    },
    parallelRun: 'Chạy song song',
    disconnect: 'Ngắt kết nối',
    jumpToNode: 'Chuyển đến nút này',
    addParallelNode: 'Thêm nút song song',
    parallel: 'SONG SONG',
    branch: 'NHÁNH',
    featuresDocLink: 'Tìm hiểu thêm',
    fileUploadTip: 'Các tính năng tải lên hình ảnh đã được nâng cấp để tải tệp lên.',
    featuresDescription: 'Nâng cao trải nghiệm người dùng ứng dụng web',
    ImageUploadLegacyTip: 'Bây giờ bạn có thể tạo các biến loại tệp trong biểu mẫu bắt đầu. Chúng tôi sẽ không còn hỗ trợ tính năng tải lên hình ảnh trong tương lai.',
    importWarning: 'Thận trọng',
    importWarningDetails: 'Sự khác biệt về phiên bản DSL có thể ảnh hưởng đến một số tính năng nhất định',
    openInExplore: 'Mở trong Khám phá',
    onFailure: 'Khi thất bại',
    addFailureBranch: 'Thêm nhánh Fail',
    loadMore: 'Tải thêm quy trình làm việc',
    noHistory: 'Không có lịch sử',
    versionHistory: 'Lịch sử phiên bản',
    publishUpdate: 'Cập nhật xuất bản',
    exportSVG: 'Xuất dưới dạng SVG',
    exitVersions: 'Phiên bản thoát',
    exportImage: 'Xuất hình ảnh',
    exportPNG: 'Xuất dưới dạng PNG',
    noExist: 'Không có biến như vậy',
    exportJPEG: 'Xuất dưới dạng JPEG',
    referenceVar: 'Biến tham chiếu',
    needAnswerNode: 'Nút Trả lời phải được thêm vào',
    addBlock: 'Thêm Node',
    needEndNode: 'Nút Kết thúc phải được thêm vào',
    tagBound: 'Số lượng ứng dụng sử dụng thẻ này',
  },
  env: {
    envPanelTitle: 'Biến Môi Trường',
    envDescription: 'Biến môi trường có thể được sử dụng để lưu trữ thông tin cá nhân và thông tin xác thực. Chúng chỉ được đọc và có thể được tách khỏi tệp DSL trong quá trình xuất.',
    envPanelButton: 'Thêm Biến',
    modal: {
      title: 'Thêm Biến Môi Trường',
      editTitle: 'Sửa Biến Môi Trường',
      type: 'Loại',
      name: 'Tên',
      namePlaceholder: 'tên môi trường',
      value: 'Giá trị',
      valuePlaceholder: 'giá trị môi trường',
      secretTip: 'Được sử dụng để xác định thông tin hoặc dữ liệu nhạy cảm, với cài đặt DSL được cấu hình để ngăn chặn rò rỉ.',
      description: 'Mô tả',
      descriptionPlaceholder: 'Mô tả biến',
    },
    export: {
      title: 'Xuất biến môi trường bí mật?',
      checkbox: 'Xuất giá trị bí mật',
      ignore: 'Xuất DSL',
      export: 'Xuất DSL với giá trị bí mật',
    },
  },
  chatVariable: {
    panelTitle: 'Biến Hội Thoại',
    panelDescription: 'Biến Hội Thoại được sử dụng để lưu trữ thông tin tương tác mà LLM cần ghi nhớ, bao gồm lịch sử hội thoại, tệp đã tải lên, tùy chọn người dùng. Chúng có thể đọc và ghi được.',
    docLink: 'Truy cập tài liệu của chúng tôi để tìm hiểu thêm.',
    button: 'Thêm Biến',
    modal: {
      title: 'Thêm Biến Hội Thoại',
      editTitle: 'Chỉnh Sửa Biến Hội Thoại',
      name: 'Tên',
      namePlaceholder: 'Tên biến',
      type: 'Loại',
      value: 'Giá Trị Mặc Định',
      valuePlaceholder: 'Giá trị mặc định, để trống nếu không đặt',
      description: 'Mô tả',
      descriptionPlaceholder: 'Mô tả biến',
      editInJSON: 'Chỉnh sửa dưới dạng JSON',
      oneByOne: 'Thêm từng cái một',
      editInForm: 'Chỉnh sửa trong Biểu mẫu',
      arrayValue: 'Giá trị',
      addArrayValue: 'Thêm Giá trị',
      objectKey: 'Khóa',
      objectType: 'Loại',
      objectValue: 'Giá Trị Mặc Định',
    },
    storedContent: 'Nội dung đã lưu',
    updatedAt: 'Cập nhật lúc ',
  },
  changeHistory: {
    title: 'Lịch sử thay đổi',
    placeholder: 'Bạn chưa thay đổi gì cả',
    clearHistory: 'Xóa lịch sử',
    hint: 'Gợi ý',
    hintText: 'Các hành động chỉnh sửa của bạn được theo dõi trong lịch sử thay đổi, được lưu trên thiết bị của bạn trong suốt phiên làm việc này. Lịch sử này sẽ bị xóa khi bạn thoát khỏi trình soạn thảo.',
    stepBackward_one: '{{count}} bước lùi',
    stepBackward_other: '{{count}} bước lùi',
    stepForward_one: '{{count}} bước tiến',
    stepForward_other: '{{count}} bước tiến',
    sessionStart: 'Bắt đầu phiên',
    currentState: 'Trạng thái hiện tại',
    noteAdd: 'Ghi chú đã thêm',
    noteChange: 'Ghi chú đã thay đổi',
    noteDelete: 'Ghi chú đã xóa',
    nodeAdd: 'Đã thêm nút',
    nodeChange: 'Node đã thay đổi',
    nodeDescriptionChange: 'Mô tả nút đã thay đổi',
    nodeTitleChange: 'Tiêu đề nút đã được thay đổi',
    nodeDelete: 'Nút đã bị xóa',
    nodeDragStop: 'Nút đã được di chuyển',
    nodeConnect: 'Nút đã kết nối',
    nodeResize: 'Kích thước nút đã được thay đổi',
    nodePaste: 'Node đã dán',
    edgeDelete: 'Nút đã bị ngắt kết nối',
  },
  errorMsg: {
    fieldRequired: '{{field}} là bắt buộc',
    authRequired: 'Yêu cầu xác thực',
    invalidJson: '{{field}} là JSON không hợp lệ',
    fields: {
      variable: 'Tên biến',
      variableValue: 'Giá trị biến',
      code: 'Mã',
      model: 'Mô hình',
      rerankModel: 'Mô hình xếp hạng lại',
      visionVariable: 'Biến tầm nhìn',
    },
    invalidVariable: 'Biến không hợp lệ',
    rerankModelRequired: 'Trước khi bật Mô hình xếp hạng lại, vui lòng xác nhận rằng mô hình đã được định cấu hình thành công trong cài đặt.',
    noValidTool: '{{field}} không chọn công cụ hợp lệ nào',
    toolParameterRequired: '{{field}}: tham số [{{param}}] là bắt buộc',
  },
  singleRun: {
    testRun: 'Chạy thử nghiệm ',
    startRun: 'Bắt đầu chạy',
    running: 'Đang chạy',
    testRunIteration: 'Lặp chạy thử nghiệm',
    back: 'Quay lại',
    iteration: 'Lặp',
    loop: 'Vòng',
  },
  tabs: {
    'tools': 'Công cụ',
    'allTool': 'Tất cả',
    'builtInTool': 'Tích hợp sẵn',
    'customTool': 'Tùy chỉnh',
    'workflowTool': 'Quy trình làm việc',
    'question-understand': 'Hiểu câu hỏi',
    'logic': 'Logic',
    'transform': 'Chuyển đổi',
    'utilities': 'Tiện ích',
    'noResult': 'Không tìm thấy kế. t quả phù hợp',
    'searchTool': 'Công cụ tìm kiếm',
    'agent': 'Chiến lược đại lý',
    'plugin': 'Plugin',
    'blocks': 'Nút',
    'searchBlock': 'Tìm kiếm nút',
    'allAdded': 'Tất cả đã được thêm vào',
    'addAll': 'Thêm tất cả',
  },
  blocks: {
    'start': 'Bắt đầu',
    'end': 'Kết thúc',
    'answer': 'Trả lời',
    'llm': 'LLM',
    'knowledge-retrieval': 'Truy xuất kiến thức',
    'question-classifier': 'Phân loại câu hỏi',
    'if-else': 'NẾU/NGƯỢC LẠI',
    'code': 'Mã',
    'template-transform': 'Mẫu',
    'http-request': 'Yêu cầu HTTP',
    'variable-assigner': 'Trình gán biến',
    'variable-aggregator': 'Trình tổng hợp biến',
    'assigner': 'Trình gán biến',
    'iteration-start': 'Bắt đầu lặp',
    'iteration': 'Lặp',
    'parameter-extractor': 'Trình trích xuất tham số',
    'list-operator': 'Toán tử danh sách',
    'document-extractor': 'Trình trích xuất tài liệu',
    'agent': 'Người đại lý',
    'loop': 'Vòng',
    'loop-end': 'Thoát vòng lặp',
    'loop-start': 'Bắt đầu vòng lặp',
  },
  blocksAbout: {
    'start': 'Định nghĩa các tham số ban đầu để khởi chạy quy trình làm việc',
    'end': 'Định nghĩa kết thúc và loại kết quả của quy trình làm việc',
    'answer': 'Định nghĩa nội dung trả lời của cuộc trò chuyện',
    'llm': 'Gọi các mô hình ngôn ngữ lớn để trả lời câu hỏi hoặc xử lý ngôn ngữ tự nhiên',
    'knowledge-retrieval': 'Cho phép truy vấn nội dung văn bản liên quan đến câu hỏi của người dùng từ cơ sở kiến thức',
    'question-classifier': 'Định nghĩa các điều kiện phân loại câu hỏi của người dùng, LLM có thể định nghĩa cách cuộc trò chuyện tiến triển dựa trên mô tả phân loại',
    'if-else': 'Cho phép phân chia quy trình làm việc thành hai nhánh dựa trên điều kiện if/else',
    'code': 'Thực thi một đoạn mã Python hoặc NodeJS để thực hiện logic tùy chỉnh',
    'template-transform': 'Chuyển đổi dữ liệu thành chuỗi bằng cú pháp mẫu Jinja',
    'http-request': 'Cho phép gửi các yêu cầu máy chủ qua giao thức HTTP',
    'variable-assigner': 'Tổng hợp các biến từ nhiều nhánh thành một biến duy nhất để cấu hình đồng nhất các nút cuối.',
    'assigner': 'Nút gán biến được sử dụng để gán giá trị cho các biến có thể ghi (như các biến hội thoại).',
    'variable-aggregator': 'Tổng hợp các biến từ nhiều nhánh thành một biến duy nhất để cấu hình đồng nhất các nút cuối.',
    'iteration': 'Thực hiện nhiều bước trên một đối tượng danh sách cho đến khi tất cả các kết quả được xuất ra.',
    'parameter-extractor': 'Sử dụng LLM để trích xuất các tham số có cấu trúc từ ngôn ngữ tự nhiên để gọi công cụ hoặc yêu cầu HTTP.',
    'document-extractor': 'Được sử dụng để phân tích cú pháp các tài liệu đã tải lên thành nội dung văn bản dễ hiểu bởi LLM.',
    'list-operator': 'Được sử dụng để lọc hoặc sắp xếp nội dung mảng.',
    'agent': 'Gọi các mô hình ngôn ngữ lớn để trả lời câu hỏi hoặc xử lý ngôn ngữ tự nhiên',
    'loop': 'Thực hiện một vòng lặp logic cho đến khi điều kiện dừng được đáp ứng hoặc số lần lặp tối đa được đạt.',
    'loop-end': 'Tương đương với "dừng lại". Nút này không có các mục cấu hình. Khi thân vòng lặp đến nút này, vòng lặp sẽ kết thúc.',
  },
  operator: {
    zoomIn: 'Phóng to',
    zoomOut: 'Thu nhỏ',
    zoomTo50: 'Phóng to 50%',
    zoomTo100: 'Phóng to 100%',
    zoomToFit: 'Phóng to vừa màn hình',
  },
  panel: {
    userInputField: 'Trường đầu vào của người dùng',
    helpLink: 'Liên kết trợ giúp',
    about: 'Giới thiệu',
    createdBy: 'Tạo bởi ',
    nextStep: 'Bước tiếp theo',
    runThisStep: 'Chạy bước này',
    checklist: 'Danh sách kiểm tra',
    checklistTip: 'Đảm bảo rằng tất cả các vấn đề đã được giải quyết trước khi xuất bản',
    checklistResolved: 'Tất cả các vấn đề đã được giải quyết',
    change: 'Thay đổi',
    optional: '(tùy chọn)',
    moveToThisNode: 'Di chuyển đến nút này',
    changeBlock: 'Thay đổi Node',
    selectNextStep: 'Chọn bước tiếp theo',
    organizeBlocks: 'Tổ chức các nút',
    addNextStep: 'Thêm bước tiếp theo trong quy trình này',
    maximize: 'Tối đa hóa Canvas',
    minimize: 'Thoát chế độ toàn màn hình',
  },
  nodes: {
    common: {
      outputVars: 'Biến đầu ra',
      insertVarTip: 'Chèn biến',
      memory: {
        memory: 'Bộ nhớ',
        memoryTip: 'Cài đặt bộ nhớ cuộc trò chuyện',
        windowSize: 'Kích thước cửa sổ',
        conversationRoleName: 'Tên vai trò cuộc trò chuyện',
        user: 'Tiền tố người dùng',
        assistant: 'Tiền tố trợ lý',
      },
      memories: {
        title: 'Bộ nhớ',
        tip: 'Bộ nhớ cuộc trò chuyện',
        builtIn: 'Tích hợp sẵn',
      },
      errorHandle: {
        none: {
          title: 'Không ai',
          desc: 'Nút sẽ ngừng chạy nếu xảy ra ngoại lệ và không được xử lý',
        },
        defaultValue: {
          title: 'Giá trị mặc định',
          desc: 'Khi xảy ra lỗi, hãy chỉ định nội dung đầu ra tĩnh.',
          tip: 'Nếu lỗi, sẽ trả về giá trị dưới đó.',
          inLog: 'Ngoại lệ nút, xuất theo giá trị mặc định.',
          output: 'Giá trị mặc định đầu ra',
        },
        failBranch: {
          title: 'Chi nhánh thất bại',
          desc: 'Khi xảy ra lỗi, nó sẽ thực thi nhánh ngoại lệ',
          customize: 'Chuyển đến canvas để tùy chỉnh logic nhánh thất bại.',
          customizeTip: 'Khi nhánh fail được kích hoạt, các ngoại lệ do các nút ném sẽ không chấm dứt quá trình. Thay vào đó, nó sẽ tự động thực thi nhánh lỗi được xác định trước, cho phép bạn linh hoạt cung cấp thông báo lỗi, báo cáo, bản sửa lỗi hoặc bỏ qua các hành động.',
          inLog: 'Node exception, sẽ tự động thực thi nhánh fail. Đầu ra nút sẽ trả về loại lỗi và thông báo lỗi và chuyển chúng đến hạ lưu.',
        },
        partialSucceeded: {
          tip: 'Có {{num}} node trong quá trình chạy bất thường, vui lòng truy tìm để kiểm tra nhật ký.',
        },
        tip: 'Chiến lược xử lý ngoại lệ, được kích hoạt khi một nút gặp phải ngoại lệ.',
        title: 'Xử lý lỗi',
      },
      retry: {
        retry: 'Thử lại',
        maxRetries: 'Số lần thử lại tối đa',
        retryInterval: 'Khoảng thời gian thử lại',
        retryTimes: 'Thử lại {{lần}} lần khi không thành công',
        retrying: 'Thử lại...',
        retrySuccessful: 'Thử lại thành công',
        retryFailed: 'Thử lại không thành công',
        retryFailedTimes: '{{lần}} lần thử lại không thành công',
        retries: '{{số}} Thử lại',
        retryOnFailure: 'Thử lại khi không thành công',
        times: 'lần',
        ms: 'Ms',
      },
      typeSwitch: {
        input: 'Giá trị đầu vào',
        variable: 'Sử dụng biến',
      },
    },
    start: {
      required: 'bắt buộc',
      inputField: 'Trường đầu vào',
      builtInVar: 'Biến tích hợp sẵn',
      outputVars: {
        query: 'Đầu vào của người dùng',
        memories: {
          des: 'Lịch sử cuộc trò chuyện',
          type: 'loại tin nhắn',
          content: 'nội dung tin nhắn',
        },
        files: 'Danh sách tệp',
      },
      noVarTip: 'Đặt các đầu vào có thể sử dụng trong Quy trình làm việc',
    },
    end: {
      outputs: 'Đầu ra',
      output: {
        type: 'loại đầu ra',
        variable: 'biến đầu ra',
      },
      type: {
        'none': 'Không có',
        'plain-text': 'Văn bản thuần',
        'structured': 'Cấu trúc',
      },
    },
    answer: {
      answer: 'Trả lời',
      outputVars: 'Biến đầu ra',
    },
    llm: {
      model: 'mô hình',
      variables: 'biến',
      context: 'ngữ cảnh',
      contextTooltip: 'Bạn có thể nhập Kiến thức làm ngữ cảnh',
      notSetContextInPromptTip: 'Để kích hoạt tính năng ngữ cảnh, vui lòng điền biến ngữ cảnh vào PROMPT.',
      prompt: 'prompt',
      roleDescription: {
        system: 'Cung cấp hướng dẫn cấp cao cho cuộc trò chuyện',
        user: 'Cung cấp hướng dẫn, câu hỏi hoặc bất kỳ đầu vào văn bản nào cho mô hình',
        assistant: 'Các phản hồi của mô hình dựa trên tin nhắn của người dùng',
      },
      addMessage: 'Thêm tin nhắn',
      vision: 'tầm nhìn',
      files: 'Tệp',
      resolution: {
        name: 'Độ phân giải',
        high: 'Cao',
        low: 'Thấp',
      },
      outputVars: {
        output: 'Nội dung được tạo',
        usage: 'Thông tin sử dụng mô hình',
      },
      singleRun: {
        variable: 'Biến',
      },
      sysQueryInUser: 'sys.query trong tin nhắn của người dùng là bắt buộc',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Vui lòng hoàn thành việc chỉnh sửa trường hiện tại trước khi lưu sơ đồ.',
        },
        promptTooltip: 'Chuyển mô tả văn bản thành cấu trúc JSON Schema chuẩn.',
        stringValidations: 'Xác thực chuỗi',
        instruction: 'Hướng dẫn',
        regenerate: 'Tái tạo',
        fieldNamePlaceholder: 'Tên trường',
        generateJsonSchema: 'Tạo Schema JSON',
        back: 'Quay lại',
        import: 'Nhập khẩu từ JSON',
        generationTip: 'Bạn có thể sử dụng ngôn ngữ tự nhiên để tạo nhanh một JSON Schema.',
        doc: 'Tìm hiểu thêm về đầu ra có cấu trúc',
        required: 'cần thiết',
        generate: 'Tạo ra',
        addField: 'Thêm trường',
        resultTip: 'Đây là kết quả đã được tạo ra. Nếu bạn không hài lòng, bạn có thể quay lại và chỉnh sửa yêu cầu của mình.',
        generating: 'Tạo sơ đồ JSON...',
        descriptionPlaceholder: 'Thêm mô tả',
        resetDefaults: 'Đặt lại',
        promptPlaceholder: 'Mô tả Sơ đồ JSON của bạn...',
        showAdvancedOptions: 'Hiển thị tùy chọn nâng cao',
        generatedResult: 'Kết quả được tạo ra',
        apply: 'Áp dụng',
        addChildField: 'Thêm trường trẻ em',
        title: 'Sơ đồ đầu ra có cấu trúc',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Biến truy vấn',
      knowledge: 'Kiến thức',
      outputVars: {
        output: 'Dữ liệu phân đoạn được truy xuất',
        content: 'Nội dung phân đoạn',
        title: 'Tiêu đề phân đoạn',
        icon: 'Biểu tượng phân đoạn',
        url: 'URL phân đoạn',
        metadata: 'Siêu dữ liệu khác',
      },
      metadata: {
        options: {
          disabled: {
            subTitle: 'Không bật lọc siêu dữ liệu',
            title: 'Tắt',
          },
          automatic: {
            desc: 'Tự động tạo điều kiện lọc siêu dữ liệu dựa trên biến truy vấn',
            title: 'Tự động',
            subTitle: 'Tự động tạo điều kiện lọc siêu dữ liệu dựa trên truy vấn của người dùng',
          },
          manual: {
            title: 'Hướng dẫn',
            subTitle: 'Thêm thủ công các điều kiện lọc siêu dữ liệu',
          },
        },
        panel: {
          add: 'Thêm điều kiện',
          conditions: 'Điều kiện',
          title: 'Điều kiện lọc siêu dữ liệu',
          select: 'Chọn biến...',
          datePlaceholder: 'Chọn một thời gian...',
          placeholder: 'Nhập giá trị',
          search: 'Tìm kiếm siêu dữ liệu',
        },
        title: 'Lọc siêu dữ liệu',
      },
    },
    http: {
      inputVars: 'Biến đầu vào',
      api: 'API',
      apiPlaceholder: 'Nhập URL, gõ ‘/’ để chèn biến',
      notStartWithHttp: 'API phải bắt đầu bằng http:// hoặc https://',
      key: 'Khóa',
      value: 'Giá trị',
      bulkEdit: 'Chỉnh sửa hàng loạt',
      keyValueEdit: 'Chỉnh sửa khóa-giá trị',
      headers: 'Tiêu đề',
      params: 'Tham số',
      body: 'Nội dung',
      outputVars: {
        body: 'Nội dung phản hồi',
        statusCode: 'Mã trạng thái phản hồi',
        headers: 'Danh sách tiêu đề phản hồi JSON',
        files: 'Danh sách tệp',
      },
      authorization: {
        'authorization': 'Ủy quyền',
        'authorizationType': 'Loại ủy quyền',
        'no-auth': 'Không có',
        'api-key': 'Khóa API',
        'auth-type': 'Loại xác thực',
        'basic': 'Cơ bản',
        'bearer': 'Bearer',
        'custom': 'Tùy chỉnh',
        'api-key-title': 'Khóa API',
        'header': 'Tiêu đề',
      },
      insertVarPlaceholder: 'gõ \'/\' để chèn biến',
      timeout: {
        title: 'Thời gian chờ',
        connectLabel: 'Thời gian chờ kết nối',
        connectPlaceholder: 'Nhập thời gian chờ kết nối tính bằng giây',
        readLabel: 'Thời gian chờ đọc',
        readPlaceholder: 'Nhập thời gian chờ đọc tính bằng giây',
        writeLabel: 'Thời gian chờ ghi',
        writePlaceholder: 'Nhập thời gian chờ ghi tính bằng giây',
      },
      binaryFileVariable: 'Biến tệp nhị phân',
      type: 'Kiểu',
      extractListPlaceholder: 'Nhập chỉ mục mục danh sách, nhập \'/\' chèn biến',
      curl: {
        title: 'Nhập từ cURL',
        placeholder: 'Dán chuỗi cURL vào đây',
      },
      verifySSL: {
        title: 'Xác thực chứng chỉ SSL',
        warningTooltip: 'Việc vô hiệu hóa xác minh SSL không được khuyến khích cho các môi trường sản xuất. Điều này chỉ nên được sử dụng trong phát triển hoặc thử nghiệm, vì nó làm cho kết nối dễ bị tổn thương trước các mối đe dọa an ninh như cuộc tấn công man-in-the-middle.',
      },
    },
    code: {
      inputVars: 'Biến đầu vào',
      outputVars: 'Biến đầu ra',
      advancedDependencies: 'Phụ thuộc nâng cao',
      advancedDependenciesTip: 'Thêm một số phụ thuộc được tải trước mà tốn nhiều thời gian hoặc không phải là mặc định tại đây',
      searchDependencies: 'Tìm kiếm phụ thuộc',
      syncFunctionSignature: 'Đồng bộ chữ ký hàm với mã',
    },
    templateTransform: {
      inputVars: 'Biến đầu vào',
      code: 'Mã',
      codeSupportTip: 'Chỉ hỗ trợ Jinja2',
      outputVars: {
        output: 'Nội dung chuyển đổi',
      },
    },
    ifElse: {
      if: 'Nếu',
      else: 'Ngược lại',
      elseDescription: 'Sử dụng để xác định logic sẽ thực hiện khi điều kiện if không được thỏa mãn.',
      and: 'và',
      or: 'hoặc',
      operator: 'Toán tử',
      notSetVariable: 'Vui lòng đặt biến trước',
      comparisonOperator: {
        'contains': 'chứa',
        'not contains': 'không chứa',
        'start with': 'bắt đầu bằng',
        'end with': 'kết thúc bằng',
        'is': 'là',
        'is not': 'không là',
        'empty': 'trống',
        'not empty': 'không trống',
        'null': 'là null',
        'not null': 'không là null',
        'regex match': 'Trận đấu Regex',
        'exists': 'Tồn tại',
        'not exists': 'không tồn tại',
        'not in': 'không có trong',
        'in': 'trong',
        'all of': 'tất cả',
        'before': 'trước',
        'after': 'sau',
      },
      enterValue: 'Nhập giá trị',
      addCondition: 'Thêm điều kiện',
      conditionNotSetup: 'Điều kiện chưa được thiết lập',
      selectVariable: 'Chọn biến...',
      optionName: {
        video: 'Video',
        image: 'Ảnh',
        url: 'Địa chỉ',
        audio: 'Âm thanh',
        doc: 'Doc',
        localUpload: 'Tải lên cục bộ',
      },
      addSubVariable: 'Biến phụ',
      select: 'Lựa',
      condition: 'Điều kiện',
    },
    variableAssigner: {
      title: 'Gán biến',
      outputType: 'Loại đầu ra',
      varNotSet: 'Biến chưa được đặt',
      noVarTip: 'Thêm các biến cần gán',
      type: {
        string: 'Chuỗi',
        number: 'Số',
        object: 'Đối tượng',
        array: 'Mảng',
      },
      aggregationGroup: 'Nhóm tổng hợp',
      aggregationGroupTip: 'Bật tính năng này cho phép trình tổng hợp biến tổng hợp nhiều bộ biến.',
      addGroup: 'Thêm nhóm',
      outputVars: {
        varDescribe: 'Đầu ra {{groupName}}',
      },
      setAssignVariable: 'Đặt biến gán',
    },
    assigner: {
      'assignedVariable': 'Biến Được Gán',
      'writeMode': 'Chế Độ Ghi',
      'writeModeTip': 'Khi BIẾN ĐƯỢC GÁN là một mảng, chế độ thêm sẽ thêm vào cuối.',
      'over-write': 'Ghi đè',
      'append': 'Thêm vào',
      'plus': 'Cộng',
      'clear': 'Xóa',
      'setVariable': 'Đặt Biến',
      'variable': 'Biến',
      'operations': {
        '-=': '-=',
        'over-write': 'Ghi đè lên',
        'clear': 'Trong',
        'append': 'Thêm',
        'title': 'Hoạt động',
        '*=': '*=',
        '/=': '/=',
        'extend': 'Mở rộng',
        '+=': '+=',
        'set': 'Cài',
        'overwrite': 'Ghi đè lên',
        'remove-last': 'Xóa Lần Cuối',
        'remove-first': 'Xóa đầu tiên',
      },
      'setParameter': 'Đặt tham số...',
      'selectAssignedVariable': 'Chọn biến được gán...',
      'noVarTip': 'Nhấp vào nút "+" để thêm biến',
      'assignedVarsDescription': 'Các biến được gán phải là các biến có thể ghi, chẳng hạn như các biến hội thoại.',
      'varNotSet': 'Biến KHÔNG được đặt',
      'noAssignedVars': 'Không có biến được gán sẵn có',
      'variables': 'Biến',
    },
    tool: {
      inputVars: 'Biến đầu vào',
      outputVars: {
        text: 'nội dung do công cụ tạo ra',
        files: {
          title: 'tệp do công cụ tạo ra',
          type: 'Loại hỗ trợ. Hiện tại chỉ hỗ trợ hình ảnh',
          transfer_method: 'Phương pháp truyền. Giá trị là remote_url hoặc local_file',
          url: 'URL hình ảnh',
          upload_file_id: 'ID tệp đã tải lên',
        },
        json: 'JSON được tạo bởi công cụ',
      },
      authorize: 'Ủy quyền',
      settings: 'Cài đặt',
      insertPlaceholder2: 'Chèn biến vào',
      insertPlaceholder1: 'Gõ hoặc nhấn',
    },
    questionClassifiers: {
      model: 'mô hình',
      inputVars: 'Biến đầu vào',
      outputVars: {
        className: 'Tên lớp',
        usage: 'Thông tin sử dụng mô hình',
      },
      class: 'Lớp',
      classNamePlaceholder: 'Viết tên lớp của bạn',
      advancedSetting: 'Cài đặt nâng cao',
      topicName: 'Tên chủ đề',
      topicPlaceholder: 'Viết tên chủ đề của bạn',
      addClass: 'Thêm lớp',
      instruction: 'Hướng dẫn',
      instructionTip: 'Nhập hướng dẫn bổ sung để giúp trình phân loại câu hỏi hiểu rõ hơn về cách phân loại câu hỏi.',
      instructionPlaceholder: 'Viết hướng dẫn của bạn',
    },
    parameterExtractor: {
      inputVar: 'Biến đầu vào',
      outputVars: {
        isSuccess: 'Thành công. Khi thành công giá trị là 1, khi thất bại giá trị là 0.',
        errorReason: 'Lý do lỗi',
        usage: 'Thông tin sử dụng mô hình',
      },
      extractParameters: 'Trích xuất tham số',
      importFromTool: 'Nhập từ công cụ',
      addExtractParameter: 'Thêm tham số trích xuất',
      addExtractParameterContent: {
        name: 'Tên',
        namePlaceholder: 'Tên tham số trích xuất',
        type: 'Loại',
        typePlaceholder: 'Loại tham số trích xuất',
        description: 'Mô tả',
        descriptionPlaceholder: 'Mô tả tham số trích xuất',
        required: 'Bắt buộc',
        requiredContent: 'Bắt buộc chỉ được sử dụng làm tài liệu tham khảo cho suy luận mô hình và không phải để xác thực bắt buộc của đầu ra tham số.',
      },
      extractParametersNotSet: 'Tham số trích xuất chưa được thiết lập',
      instruction: 'Hướng dẫn',
      instructionTip: 'Nhập hướng dẫn bổ sung để giúp trình trích xuất tham số hiểu rõ hơn về cách trích xuất tham số.',
      advancedSetting: 'Cài đặt nâng cao',
      reasoningMode: 'Chế độ suy luận',
      reasoningModeTip: 'Bạn có thể chọn chế độ suy luận phù hợp dựa trên khả năng của mô hình để phản hồi các hướng dẫn về việc gọi hàm hoặc prompt.',
    },
    iteration: {
      deleteTitle: 'Xóa nút lặp?',
      deleteDesc: 'Xóa nút lặp sẽ xóa tất cả các nút con',
      input: 'Đầu vào',
      output: 'Biến đầu ra',
      iteration_one: '{{count}} Lặp',
      iteration_other: '{{count}} Lặp',
      currentIteration: 'Lặp hiện tại',
      ErrorMethod: {
        operationTerminated: 'Chấm dứt',
        removeAbnormalOutput: 'loại bỏ-bất thường-đầu ra',
        continueOnError: 'Tiếp tục lỗi',
      },
      comma: ',',
      error_other: '{{đếm}} Lỗi',
      error_one: '{{đếm}} Lỗi',
      MaxParallelismTitle: 'Song song tối đa',
      parallelPanelDesc: 'Ở chế độ song song, các tác vụ trong quá trình lặp hỗ trợ thực thi song song.',
      parallelMode: 'Chế độ song song',
      parallelModeEnableTitle: 'Đã bật Chế độ song song',
      errorResponseMethod: 'Phương pháp phản hồi lỗi',
      MaxParallelismDesc: 'Tính song song tối đa được sử dụng để kiểm soát số lượng tác vụ được thực hiện đồng thời trong một lần lặp.',
      answerNodeWarningDesc: 'Cảnh báo chế độ song song: Các nút trả lời, bài tập biến hội thoại và các thao tác đọc/ghi liên tục trong các lần lặp có thể gây ra ngoại lệ.',
      parallelModeEnableDesc: 'Trong chế độ song song, các tác vụ trong các lần lặp hỗ trợ thực thi song song. Bạn có thể định cấu hình điều này trong bảng thuộc tính ở bên phải.',
      parallelModeUpper: 'CHẾ ĐỘ SONG SONG',
    },
    note: {
      editor: {
        openLink: 'Mở',
        italic: 'Nghiêng',
        link: 'Liên kết',
        medium: 'Đau vừa',
        small: 'Nhỏ',
        placeholder: 'Viết ghi chú của bạn...',
        large: 'Lớn',
        showAuthor: 'Hiển thị tác giả',
        bulletList: 'Danh sách dấu đầu dòng',
        bold: 'Dũng cảm',
        unlink: 'Hủy liên kết',
        invalidUrl: 'URL không hợp lệ',
        strikethrough: 'Gạch ngang',
        enterUrl: 'Nhập URL...',
      },
      addNote: 'Thêm ghi chú',
    },
    docExtractor: {
      outputVars: {
        text: 'Văn bản trích xuất',
      },
      learnMore: 'Tìm hiểu thêm',
      inputVar: 'Biến đầu vào',
      supportFileTypes: 'Các loại tệp hỗ trợ: {{types}}.',
    },
    listFilter: {
      outputVars: {
        last_record: 'Kỷ lục cuối cùng',
        first_record: 'Kỷ lục đầu tiên',
        result: 'Lọc kết quả',
      },
      orderBy: 'Đặt hàng theo',
      selectVariableKeyPlaceholder: 'Chọn khóa biến phụ',
      inputVar: 'Biến đầu vào',
      desc: 'DESC',
      filterConditionKey: 'Khóa điều kiện bộ lọc',
      filterConditionComparisonValue: 'Giá trị Điều kiện lọc',
      limit: 'Top N',
      filterCondition: 'Điều kiện lọc',
      asc: 'ASC',
      filterConditionComparisonOperator: 'Toán tử so sánh điều kiện bộ lọc',
      extractsCondition: 'Giải nén mục N',
    },
    agent: {
      strategy: {
        selectTip: 'Chọn chiến lược tác nhân',
        searchPlaceholder: 'Chiến lược tác nhân tìm kiếm',
        shortLabel: 'Chiến lược',
        configureTipDesc: 'Sau khi cấu hình chiến lược tác nhân, nút này sẽ tự động tải các cấu hình còn lại. Chiến lược sẽ ảnh hưởng đến cơ chế suy luận công cụ nhiều bước.',
        tooltip: 'Các chiến lược Agentic khác nhau xác định cách hệ thống lập kế hoạch và thực hiện các cuộc gọi công cụ nhiều bước',
        label: 'Chiến lược đại lý',
        configureTip: 'Vui lòng định cấu hình chiến lược tác nhân.',
      },
      pluginInstaller: {
        install: 'Cài đặt',
        installing: 'Cài đặt',
      },
      modelNotInMarketplace: {
        title: 'Mô hình chưa được cài đặt',
        manageInPlugins: 'Quản lý trong Plugins',
        desc: 'Mô hình này được cài đặt từ kho lưu trữ cục bộ hoặc GitHub. Vui lòng sử dụng sau khi cài đặt.',
      },
      modelNotSupport: {
        desc: 'Phiên bản plugin đã cài đặt không cung cấp mô hình này.',
        title: 'Mô hình không được hỗ trợ',
        descForVersionSwitch: 'Phiên bản plugin đã cài đặt không cung cấp mô hình này. Nhấp để chuyển đổi phiên bản.',
      },
      modelSelectorTooltips: {
        deprecated: 'Mô hình này không còn được dùng nữa',
      },
      outputVars: {
        files: {
          title: 'Tệp do tác nhân tạo',
          transfer_method: 'Phương thức chuyển khoản. Giá trị là remote_url hoặc local_file',
          upload_file_id: 'Tải lên id tệp',
          type: 'Loại hỗ trợ. Bây giờ chỉ hỗ trợ hình ảnh',
          url: 'URL hình ảnh',
        },
        json: 'JSON do tác nhân tạo',
        text: 'Nội dung do tác nhân tạo',
      },
      checkList: {
        strategyNotSelected: 'Chiến lược không được chọn',
      },
      installPlugin: {
        install: 'Cài đặt',
        cancel: 'Hủy',
        title: 'Cài đặt Plugin',
        desc: 'Giới thiệu cài đặt plugin sau',
        changelog: 'Nhật ký thay đổi',
      },
      toolNotAuthorizedTooltip: '{{công cụ}} Không được ủy quyền',
      unsupportedStrategy: 'Chiến lược không được hỗ trợ',
      toolNotInstallTooltip: '{{tool}} không được cài đặt',
      strategyNotFoundDescAndSwitchVersion: 'Phiên bản plugin đã cài đặt không cung cấp chiến lược này. Nhấp để chuyển đổi phiên bản.',
      strategyNotInstallTooltip: '{{strategy}} không được cài đặt',
      modelNotInstallTooltip: 'Mô hình này không được cài đặt',
      strategyNotSet: 'Chiến lược tác nhân không được thiết lập',
      linkToPlugin: 'Liên kết đến Plugins',
      configureModel: 'Định cấu hình mô hình',
      pluginNotInstalledDesc: 'Plugin này được cài đặt từ GitHub. Vui lòng vào Plugins để cài đặt lại',
      modelNotSelected: 'Mô hình không được chọn',
      learnMore: 'Tìm hiểu thêm',
      pluginNotInstalled: 'Plugin này chưa được cài đặt',
      model: 'mẫu',
      pluginNotFoundDesc: 'Plugin này được cài đặt từ GitHub. Vui lòng vào Plugins để cài đặt lại',
      maxIterations: 'Số lần lặp lại tối đa',
      tools: 'Công cụ',
      notAuthorized: 'Không được ủy quyền',
      strategyNotFoundDesc: 'Phiên bản plugin đã cài đặt không cung cấp chiến lược này.',
      toolbox: 'hộp công cụ',
      clickToViewParameterSchema: 'Nhấp để xem sơ đồ tham số',
      parameterSchema: 'Sơ đồ Tham số',
    },
    loop: {
      ErrorMethod: {
        continueOnError: 'Tiếp tục khi có lỗi',
        removeAbnormalOutput: 'Xóa đầu ra bất thường',
        operationTerminated: 'Chấm dứt',
      },
      breakConditionTip: 'Chỉ có thể tham chiếu đến các biến trong vòng lặp có điều kiện kết thúc và các biến hội thoại.',
      deleteTitle: 'Xóa nút vòng lặp?',
      variableName: 'Tên Biến',
      input: 'Nhập',
      exitConditionTip: 'Một nút vòng lặp cần ít nhất một điều kiện thoát.',
      breakCondition: 'Điều kiện dừng vòng lặp',
      totalLoopCount: 'Tổng số lần lặp: {{count}}',
      setLoopVariables: 'Đặt biến trong phạm vi vòng lặp',
      currentLoopCount: 'Số vòng lặp hiện tại: {{count}}',
      deleteDesc: 'Xóa nút vòng sẽ xóa tất cả các nút con',
      inputMode: 'Chế độ đầu vào',
      currentLoop: 'Vòng lặp hiện tại',
      loopMaxCountError: 'Vui lòng nhập số vòng lặp tối đa hợp lệ, trong khoảng từ 1 đến {{maxCount}}',
      loop_other: '{{count}} Vòng lặp',
      finalLoopVariables: 'Biến Vòng Lặp Cuối',
      initialLoopVariables: 'Biến Vòng Lặp Đầu Tiên',
      loop_one: '{{count}} Vòng lặp',
      error_other: '{{count}} Lỗi',
      output: 'Biến đầu ra',
      errorResponseMethod: 'Phương pháp phản hồi lỗi',
      loopMaxCount: 'Số lần lặp tối đa',
      comma: ',',
      loopVariables: 'Biến Lặp',
      error_one: '{{count}} Lỗi',
      loopNode: 'Nút Lặp',
    },
  },
  tracing: {
    stopBy: 'Dừng bởi {{user}}',
  },
  variableReference: {
    noAssignedVars: 'Không có biến được gán sẵn có',
    noAvailableVars: 'Không có biến khả dụng',
    assignedVarsDescription: 'Các biến được gán phải là các biến có thể ghi, chẳng hạn như',
    conversationVars: 'Biến cuộc trò chuyện',
    noVarsForOperation: 'Không có biến nào có sẵn để gán với hoạt động đã chọn.',
  },
  versionHistory: {
    filter: {
      onlyYours: 'Chỉ của bạn',
      empty: 'Không tìm thấy lịch sử phiên bản phù hợp',
      onlyShowNamedVersions: 'Chỉ hiển thị các phiên bản có tên',
      reset: 'Đặt lại bộ lọc',
      all: 'Tất cả',
    },
    editField: {
      releaseNotesLengthLimit: 'Ghi chú phát hành không được vượt quá {{limit}} ký tự.',
      title: 'Tiêu đề',
      releaseNotes: 'Ghi chú phát hành',
      titleLengthLimit: 'Tiêu đề không được vượt quá {{limit}} ký tự',
    },
    action: {
      deleteFailure: 'Xóa phiên bản thất bại',
      updateFailure: 'Cập nhật phiên bản không thành công',
      deleteSuccess: 'Phiên bản đã bị xóa',
      updateSuccess: 'Phiên bản đã được cập nhật',
      restoreSuccess: 'Phiên bản đã được khôi phục',
      restoreFailure: 'Không thể khôi phục phiên bản',
    },
    defaultName: 'Phiên bản không được đặt tên',
    releaseNotesPlaceholder: 'Mô tả những gì đã thay đổi',
    deletionTip: 'Việc xóa là không thể phục hồi, vui lòng xác nhận.',
    currentDraft: 'Dự thảo hiện tại',
    editVersionInfo: 'Chỉnh sửa thông tin phiên bản',
    latest: 'Mới nhất',
    nameThisVersion: 'Đặt tên cho phiên bản này',
    restorationTip: 'Sau khi phục hồi phiên bản, bản nháp hiện tại sẽ bị ghi đè.',
    title: 'Các phiên bản',
  },
  debug: {
    noData: {
      runThisNode: 'Chạy nút này',
      description: 'Kết quả của lần chạy cuối cùng sẽ được hiển thị ở đây',
    },
    variableInspect: {
      trigger: {
        clear: 'Rõ ràng',
        stop: 'Dừng lại',
        normal: 'Kiểm tra Biến',
        cached: 'Xem các biến được lưu trong bộ nhớ cache',
        running: 'Trạng thái đang chạy của bộ nhớ đệm',
      },
      envNode: 'Môi trường',
      edited: 'Biên soạn',
      chatNode: 'Cuộc trò chuyện',
      view: 'Xem nhật ký',
      clearAll: 'Đặt lại tất cả',
      reset: 'Đặt lại thành giá trị của lần chạy cuối cùng',
      resetConversationVar: 'Đặt lại biến cuộc trò chuyện về giá trị mặc định',
      title: 'Kiểm tra Biến',
      systemNode: 'Hệ thống',
      clearNode: 'Xóa biến đã được lưu trong bộ nhớ cache',
      emptyLink: 'Tìm hiểu thêm',
      emptyTip: 'Sau khi bước qua một nút trên canvas hoặc chạy một nút từng bước, bạn có thể xem giá trị hiện tại của biến nút trong Variable Inspect.',
    },
    settingsTab: 'Cài đặt',
    lastRunTab: 'Chạy Lần Cuối',
  },
}

export default translation
