const translation = {
  category: {
    all: 'Tất cả',
    bundles: 'Bó',
    extensions: 'Phần mở rộng',
    tools: 'Công cụ',
    agents: 'Chiến lược đại lý',
    models: '<PERSON><PERSON> hình',
  },
  categorySingle: {
    agent: 'Chiến lược đại lý',
    tool: 'Công cụ',
    extension: 'Phần mở rộng',
    model: 'Mẫu',
    bundle: 'Bó',
  },
  list: {
    source: {
      marketplace: 'Cài đặt từ Marketplace',
      local: 'Cài đặt từ tệp gói cục bộ',
      github: 'Cài đặt từ GitHub',
    },
    noInstalled: 'Không có plugin nào được cài đặt',
    notFound: 'Không tìm thấy plugin',
  },
  source: {
    marketplace: 'Chợ',
    local: 'Tệp gói cục bộ',
    github: 'GitHub',
  },
  detailPanel: {
    categoryTip: {
      local: 'Plugin cục bộ',
      debugging: 'Plugin gỡ lỗi',
      marketplace: '<PERSON><PERSON><PERSON><PERSON> cài đặt từ Marketplace',
      github: '<PERSON><PERSON>i đặt từ Github',
    },
    operation: {
      detail: 'Chi tiết',
      update: 'Cậ<PERSON> nhật',
      viewDetail: 'xem chi tiết',
      info: 'Thông tin plugin',
      remove: 'Triệt',
      install: 'Cài đặt',
      checkUpdate: 'Kiểm tra cập nhật',
    },
    toolSelector: {
      descriptionPlaceholder: 'Mô tả ngắn gọn về mục đích của công cụ, ví dụ: lấy nhiệt độ cho một vị trí cụ thể.',
      params: 'CẤU HÌNH LÝ LUẬN',
      toolLabel: 'Công cụ',
      descriptionLabel: 'Mô tả công cụ',
      unsupportedContent2: 'Nhấp để chuyển đổi phiên bản.',
      auto: 'Tự động',
      placeholder: 'Chọn một công cụ...',
      paramsTip1: 'Kiểm soát các tham số suy luận LLM.',
      uninstalledTitle: 'Công cụ chưa được cài đặt',
      unsupportedContent: 'Phiên bản plugin đã cài đặt không cung cấp hành động này.',
      uninstalledContent: 'Plugin này được cài đặt từ kho lưu trữ cục bộ / GitHub. Vui lòng sử dụng sau khi cài đặt.',
      paramsTip2: 'Khi tắt \'Tự động\', giá trị mặc định sẽ được sử dụng.',
      uninstalledLink: 'Quản lý trong Plugins',
      title: 'Thêm công cụ',
      settings: 'CÀI ĐẶT NGƯỜI DÙNG',
      empty: 'Nhấp vào nút \'+\' để thêm công cụ. Bạn có thể thêm nhiều công cụ.',
      unsupportedTitle: 'Hành động không được hỗ trợ',
      toolSetting: 'Cài đặt công cụ',
      unsupportedMCPTool: 'Phiên bản plugin chiến lược đại lý được chọn hiện tại không hỗ trợ công cụ MCP.',
    },
    switchVersion: 'Chuyển đổi phiên bản',
    endpointDisableTip: 'Tắt điểm cuối',
    endpointDeleteTip: 'Xóa điểm cuối',
    configureApp: 'Định cấu hình ứng dụng',
    configureModel: 'Định cấu hình mô hình',
    endpointsTip: 'Plugin này cung cấp các chức năng cụ thể thông qua các điểm cuối và bạn có thể định cấu hình nhiều bộ điểm cuối cho không gian làm việc hiện tại.',
    endpointDisableContent: 'Bạn có muốn vô hiệu hóa {{name}} không?',
    strategyNum: '{{số}} {{chiến lược}} BAO GỒM',
    endpoints: 'Điểm cuối',
    actionNum: '{{số}} {{hành động}} BAO GỒM',
    configureTool: 'Công cụ định cấu hình',
    modelNum: '{{số}} CÁC MÔ HÌNH BAO GỒM',
    serviceOk: 'Dịch vụ OK',
    endpointsDocLink: 'Xem tài liệu',
    endpointsEmpty: 'Nhấp vào nút \'+\' để thêm điểm cuối',
    endpointModalDesc: 'Sau khi định cấu hình, các tính năng do plugin cung cấp thông qua điểm cuối API có thể được sử dụng.',
    endpointDeleteContent: 'Bạn có muốn xóa {{name}} không?',
    endpointModalTitle: 'Điểm cuối thiết lập',
    disabled: 'Tàn tật',
    deprecation: {
      reason: {
        noMaintainer: 'không có người bảo trì',
        ownershipTransferred: 'quyền sở hữu được chuyển nhượng',
        businessAdjustments: 'điều chỉnh kinh doanh',
      },
      noReason: 'Plugin này đã bị loại bỏ và sẽ không còn được cập nhật.',
      onlyReason: 'Plugin này đã bị ngừng hỗ trợ do {{deprecatedReason}} và sẽ không còn được cập nhật nữa.',
      fullMessage: 'Plugin này đã bị ngừng sử dụng do {{deprecatedReason}}, và sẽ không còn được cập nhật nữa. Vui lòng sử dụng <CustomLink href=\'https://example.com/\'>{{-alternativePluginId}}</CustomLink> thay thế.',
    },
  },
  debugInfo: {
    title: 'Gỡ lỗi',
    viewDocs: 'Xem tài liệu',
  },
  privilege: {
    whoCanInstall: 'Ai có thể cài đặt và quản lý plugin?',
    everyone: 'Ai ai',
    whoCanDebug: 'Ai có thể gỡ lỗi plugin?',
    title: 'Tùy chọn plugin',
    admins: 'Quản trị viên',
    noone: 'Không ai',
  },
  pluginInfoModal: {
    release: 'Phát hành',
    repository: 'Kho',
    title: 'Thông tin plugin',
    packageName: 'Gói',
  },
  action: {
    delete: 'Xóa plugin',
    deleteContentRight: 'plugin?',
    usedInApps: 'Plugin này đang được sử dụng trong các ứng dụng {{num}}.',
    pluginInfo: 'Thông tin plugin',
    checkForUpdates: 'Kiểm tra thông tin cập nhật',
    deleteContentLeft: 'Bạn có muốn xóa',
  },
  installModal: {
    labels: {
      package: 'Gói',
      repository: 'Kho',
      version: 'Phiên bản',
    },
    close: 'Đóng',
    installFailedDesc: 'Plugin đã được cài đặt không thành công.',
    cancel: 'Hủy',
    install: 'Cài đặt',
    dropPluginToInstall: 'Thả gói plugin vào đây để cài đặt',
    readyToInstallPackage: 'Giới thiệu cài đặt plugin sau',
    uploadingPackage: 'Tải lên {{packageName}}...',
    installing: 'Cài đặt...',
    installedSuccessfully: 'Cài đặt thành công',
    readyToInstall: 'Giới thiệu cài đặt plugin sau',
    next: 'Sau',
    readyToInstallPackages: 'Chuẩn bị cài đặt các plugin {{num}} sau',
    pluginLoadErrorDesc: 'Plugin này sẽ không được cài đặt',
    fromTrustSource: 'Hãy đảm bảo rằng bạn chỉ cài đặt các plugin từ <trustSource>một nguồn đáng tin cậy</trustSource>.',
    installedSuccessfullyDesc: 'Plugin đã được cài đặt thành công.',
    uploadFailed: 'Tải lên không thành công',
    installPlugin: 'Cài đặt Plugin',
    installFailed: 'Cài đặt không thành công',
    installComplete: 'Cài đặt hoàn tất',
    back: 'Lưng',
    pluginLoadError: 'Lỗi tải plugin',
    installWarning: 'Plugin này không được phép cài đặt.',
  },
  installFromGitHub: {
    installFailed: 'Cài đặt không thành công',
    updatePlugin: 'Cập nhật plugin từ GitHub',
    gitHubRepo: 'Kho lưu trữ GitHub',
    selectPackage: 'Chọn gói',
    selectVersionPlaceholder: 'Vui lòng chọn một phiên bản',
    installedSuccessfully: 'Cài đặt thành công',
    installPlugin: 'Cài đặt plugin từ GitHub',
    uploadFailed: 'Tải lên không thành công',
    selectPackagePlaceholder: 'Vui lòng chọn một gói',
    selectVersion: 'Chọn phiên bản',
    installNote: 'Hãy đảm bảo rằng bạn chỉ cài đặt các plugin từ một nguồn đáng tin cậy.',
  },
  upgrade: {
    upgrade: 'Cài đặt',
    upgrading: 'Cài đặt...',
    successfulTitle: 'Cài đặt thành công',
    title: 'Cài đặt Plugin',
    usedInApps: 'Được sử dụng trong các ứng dụng {{num}}',
    description: 'Giới thiệu cài đặt plugin sau',
    close: 'Đóng',
  },
  error: {
    noReleasesFound: 'Không tìm thấy bản phát hành. Vui lòng kiểm tra kho lưu trữ GitHub hoặc URL đầu vào.',
    fetchReleasesError: 'Không thể truy xuất bản phát hành. Vui lòng thử lại sau.',
    inValidGitHubUrl: 'URL GitHub không hợp lệ. Vui lòng nhập URL hợp lệ theo định dạng: https://github.com/owner/repo',
  },
  marketplace: {
    sortOption: {
      newlyReleased: 'Mới phát hành',
      mostPopular: 'Phổ biến nhất',
      firstReleased: 'Phát hành lần đầu tiên',
      recentlyUpdated: 'Cập nhật gần đây',
    },
    empower: 'Hỗ trợ phát triển AI của bạn',
    viewMore: 'Xem thêm',
    difyMarketplace: 'Thị trường Dify',
    discover: 'Khám phá',
    pluginsResult: '{{num}} kết quả',
    moreFrom: 'Các ứng dụng khác từ Marketplace',
    sortBy: 'Thành phố đen',
    noPluginFound: 'Không tìm thấy plugin nào',
    and: 'và',
    verifiedTip: 'Được xác nhận bởi Dify',
    partnerTip: 'Được xác nhận bởi một đối tác của Dify',
  },
  task: {
    installingWithError: 'Cài đặt {{installingLength}} plugins, {{successLength}} thành công, {{errorLength}} không thành công',
    installing: 'Cài đặt {{installingLength}} plugins, 0 xong.',
    installingWithSuccess: 'Cài đặt {{installingLength}} plugins, {{successLength}} thành công.',
    installError: '{{errorLength}} plugin không cài đặt được, nhấp để xem',
    installedError: '{{errorLength}} plugin không cài đặt được',
    clearAll: 'Xóa tất cả',
  },
  from: 'Từ',
  installAction: 'Cài đặt',
  searchInMarketplace: 'Tìm kiếm trên Marketplace',
  endpointsEnabled: '{{num}} bộ điểm cuối được kích hoạt',
  install: '{{num}} lượt cài đặt',
  findMoreInMarketplace: 'Tìm thêm trong Marketplace',
  search: 'Tìm kiếm',
  searchCategories: 'Danh mục tìm kiếm',
  installPlugin: 'Cài đặt plugin',
  searchPlugins: 'Tìm kiếm plugin',
  fromMarketplace: 'Từ Marketplace',
  allCategories: 'Tất cả các danh mục',
  searchTools: 'Công cụ tìm kiếm...',
  installFrom: 'CÀI ĐẶT TỪ',
  metadata: {
    title: 'Plugin',
  },
  difyVersionNotCompatible: 'Phiên bản Dify hiện tại không tương thích với plugin này, vui lòng nâng cấp lên phiên bản tối thiểu cần thiết: {{minimalDifyVersion}}',
  requestAPlugin: 'Yêu cầu một plugin',
  publishPlugins: 'Xuất bản plugin',
  auth: {
    custom: 'Tùy chỉnh',
    saveOnly: 'Chỉ lưu lại',
    authorizationName: 'Tên ủy quyền',
    addOAuth: 'Thêm OAuth',
    oauthClient: 'Khách hàng OAuth',
    useOAuth: 'Sử dụng OAuth',
    saveAndAuth: 'Lưu và Xác nhận',
    authorizations: 'Chấp thuận',
    setupOAuth: 'Thiết lập OAuth Client',
    authRemoved: 'Chính quyền đã loại bỏ',
    useOAuthAuth: 'Sử dụng ủy quyền OAuth',
    authorization: 'Ủy quyền',
    useApiAuth: 'Cấu hình ủy quyền khóa API',
    default: 'Mặc định',
    addApi: 'Thêm khóa API',
    oauthClientSettings: 'Cài đặt khách hàng OAuth',
    workspaceDefault: 'Mặc định không gian làm việc',
    useApi: 'Sử dụng khóa API',
    setDefault: 'Đặt làm mặc định',
    useApiAuthDesc: 'Sau khi cấu hình thông tin xác thực, tất cả các thành viên trong không gian làm việc có thể sử dụng công cụ này khi điều phối các ứng dụng.',
    clientInfo: 'Vì không tìm thấy bí mật khách hàng hệ thống cho nhà cung cấp công cụ này, cần thiết lập thủ công, đối với redirect_uri, vui lòng sử dụng',
  },
  deprecated: 'Đã bị ngưng sử dụng',
  autoUpdate: {
    strategy: {
      disabled: {
        name: 'Khuyết tật',
        description: 'Các plugin sẽ không tự động cập nhật',
      },
      fixOnly: {
        name: 'Chỉ sửa chữa',
        selectedDescription: 'Tự động cập nhật chỉ cho các phiên bản bản vá',
      },
      latest: {
        name: 'Mới nhất',
        description: 'Luôn cập nhật lên phiên bản mới nhất',
        selectedDescription: 'Luôn cập nhật lên phiên bản mới nhất',
      },
    },
    upgradeMode: {
      partial: 'Chỉ được chọn',
      exclude: 'Loại trừ đã chọn',
      all: 'Cập nhật tất cả',
    },
    upgradeModePlaceholder: {
      exclude: 'Các plugin được chọn sẽ không tự động cập nhật',
      partial: 'Chỉ những plugin được chọn mới tự động cập nhật. Hiện tại không có plugin nào được chọn, vì vậy sẽ không có plugin nào tự động cập nhật.',
    },
    operation: {
      clearAll: 'Xóa tất cả',
      select: 'Chọn plugin',
    },
    pluginDowngradeWarning: {
      exclude: 'Loại trừ khỏi cập nhật tự động',
      downgrade: 'Giảm cấp vẫn vậy',
      description: 'Chức năng tự động cập nhật hiện đang được bật cho plugin này. Việc hạ cấp phiên bản có thể khiến các thay đổi của bạn bị ghi đè trong lần cập nhật tự động tiếp theo.',
      title: 'Hạ cấp Plugin',
    },
    noPluginPlaceholder: {
      noInstalled: 'Không có plugin nào được cài đặt',
      noFound: 'Không tìm thấy plugin nào',
    },
    updateTimeTitle: 'Thời gian cập nhật',
    updateTime: 'Thời gian cập nhật',
    automaticUpdates: 'Cập nhật tự động',
    nextUpdateTime: 'Cập nhật tự động tiếp theo: {{time}}',
    specifyPluginsToUpdate: 'Chỉ định các plugin để cập nhật',
    excludeUpdate: 'Các plugin {{num}} sau đây sẽ không tự động cập nhật',
    updateSettings: 'Cập nhật cài đặt',
    partialUPdate: 'Chỉ có {{num}} plugin sau đây sẽ tự động cập nhật',
    changeTimezone: 'Để thay đổi múi giờ, hãy vào <setTimezone>Cài đặt</setTimezone>',
  },
}

export default translation
