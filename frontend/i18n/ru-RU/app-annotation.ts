const translation = {
  title: 'Аннотации',
  name: 'Ответить на аннотацию',
  editBy: 'Ответ отредактирован {{author}}',
  noData: {
    title: 'Нет аннотаций',
    description: 'Вы можете редактировать аннотации во время отладки приложения или импортировать их массово здесь для получения качественного ответа.',
  },
  table: {
    header: {
      question: 'вопрос',
      answer: 'ответ',
      createdAt: 'создано',
      hits: 'попаданий',
      actions: 'действия',
      addAnnotation: 'Добавить аннотацию',
      bulkImport: 'Массовый импорт',
      bulkExport: 'Массовый экспорт',
      clearAll: 'Очистить все аннотации',
    },
  },
  editModal: {
    title: 'Редактировать ответ аннотации',
    queryName: 'Запрос пользователя',
    answerName: 'Storyteller Bot',
    yourAnswer: 'Ваш ответ',
    answerPlaceholder: 'Введите ваш ответ здесь',
    yourQuery: 'Ваш запрос',
    queryPlaceholder: 'Введите ваш запрос здесь',
    removeThisCache: 'Удалить эту аннотацию',
    createdAt: 'Создано',
  },
  addModal: {
    title: 'Добавить ответ аннотации',
    queryName: 'Вопрос',
    answerName: 'Ответ',
    answerPlaceholder: 'Введите ответ здесь',
    queryPlaceholder: 'Введите вопрос здесь',
    createNext: 'Добавить еще один аннотированный ответ',
  },
  batchModal: {
    title: 'Массовый импорт',
    csvUploadTitle: 'Перетащите сюда ваш CSV-файл или ',
    browse: 'выберите файл',
    tip: 'CSV-файл должен соответствовать следующей структуре:',
    question: 'вопрос',
    answer: 'ответ',
    contentTitle: 'содержимое фрагмента',
    content: 'содержимое',
    template: 'Скачать шаблон здесь',
    cancel: 'Отмена',
    run: 'Запустить пакет',
    runError: 'Ошибка запуска пакета',
    processing: 'В процессе пакетной обработки',
    completed: 'Импорт завершен',
    error: 'Ошибка импорта',
    ok: 'ОК',
  },
  errorMessage: {
    answerRequired: 'Ответ обязателен',
    queryRequired: 'Вопрос обязателен',
  },
  viewModal: {
    annotatedResponse: 'Ответ аннотации',
    hitHistory: 'История попаданий',
    hit: 'Попадание',
    hits: 'Попадания',
    noHitHistory: 'Нет истории попаданий',
  },
  hitHistoryTable: {
    query: 'Запрос',
    match: 'Совпадение',
    response: 'Ответ',
    source: 'Источник',
    score: 'Оценка',
    time: 'Время',
  },
  initSetup: {
    title: 'Начальная настройка ответа аннотации',
    configTitle: 'Настройка ответа аннотации',
    confirmBtn: 'Сохранить и включить',
    configConfirmBtn: 'Сохранить',
  },
  embeddingModelSwitchTip: 'Модель векторизации текста аннотаций, переключение между моделями будет осуществлено повторно, что приведет к дополнительным затратам.',
}

export default translation
