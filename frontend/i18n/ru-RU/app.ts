const translation = {
  createApp: 'СОЗДАТЬ ПРИЛОЖЕНИЕ',
  types: {
    all: 'Все',
    chatbot: 'Чат-бот',
    agent: 'Агент',
    workflow: 'Рабочий процесс',
    completion: 'Завершение',
    advanced: 'Чатфлоу',
    basic: 'Основной',
  },
  duplicate: 'Дублировать',
  duplicateTitle: 'Дублировать приложение',
  export: 'Экспортировать DSL',
  exportFailed: 'Ошибка экспорта DSL.',
  importDSL: 'Импортировать файл DSL',
  createFromConfigFile: 'Создать из файла DSL',
  importFromDSL: 'Импортировать из DSL',
  importFromDSLFile: 'Из файла DSL',
  importFromDSLUrl: 'Из URL',
  importFromDSLUrlPlaceholder: 'Вставьте ссылку DSL сюда',
  deleteAppConfirmTitle: 'Удалить это приложение?',
  deleteAppConfirmContent:
    'Удаление приложения необратимо. Пользователи больше не смогут получить доступ к вашему приложению, и все настройки подсказок и журналы будут безвозвратно удалены.',
  appDeleted: 'Приложение удалено',
  appDeleteFailed: 'Не удалось удалить приложение',
  join: 'Присоединяйтесь к сообществу',
  communityIntro:
    'Общайтесь с членами команды, участниками и разработчиками на разных каналах.',
  roadmap: 'Посмотреть наш roadmap',
  newApp: {
    startFromBlank: 'Создать с нуля',
    startFromTemplate: 'Создать из шаблона',
    captionAppType: 'Какой тип приложения вы хотите создать?',
    chatbotDescription: 'Создайте приложение на основе чата. Это приложение использует формат вопросов и ответов, позволяя общаться непрерывно.',
    completionDescription: 'Создайте приложение, которое генерирует высококачественный текст на основе подсказок, например, генерирует статьи, резюме, переводы и многое другое.',
    completionWarning: 'Этот тип приложения больше не будет поддерживаться.',
    agentDescription: 'Создайте интеллектуального агента, который может автономно выбирать инструменты для выполнения задач',
    workflowDescription: 'Создайте приложение, которое генерирует высококачественный текст на основе рабочего процесса, организованного с высокой степенью настройки. Подходит для опытных пользователей.',
    workflowWarning: 'В настоящее время находится в бета-версии',
    chatbotType: 'Метод организации чат-бота',
    basic: 'Базовый',
    basicTip: 'Для начинающих, можно переключиться на Chatflow позже',
    basicFor: 'ДЛЯ НАЧИНАЮЩИХ',
    basicDescription: 'Базовый конструктор позволяет создать приложение чат-бота с помощью простых настроек, без возможности изменять встроенные подсказки. Подходит для начинающих.',
    advanced: 'Chatflow',
    advancedFor: 'Для продвинутых пользователей',
    advancedDescription: 'Организация рабочего процесса организует чат-ботов в виде рабочих процессов, предлагая высокую степень настройки, включая возможность редактирования встроенных подсказок. Подходит для опытных пользователей.',
    captionName: 'Значок и название приложения',
    appNamePlaceholder: 'Дайте вашему приложению имя',
    captionDescription: 'Описание',
    appDescriptionPlaceholder: 'Введите описание приложения',
    useTemplate: 'Использовать этот шаблон',
    previewDemo: 'Предварительный просмотр',
    chatApp: 'Ассистент',
    chatAppIntro:
      'Я хочу создать приложение на основе чата. Это приложение использует формат вопросов и ответов, позволяя общаться непрерывно.',
    agentAssistant: 'Новый Ассистент Агента',
    completeApp: 'Генератор текста',
    completeAppIntro:
      'Я хочу создать приложение, которое генерирует высококачественный текст на основе подсказок, например, генерирует статьи, резюме, переводы и многое другое.',
    showTemplates: 'Я хочу выбрать из шаблона',
    hideTemplates: 'Вернуться к выбору режима',
    Create: 'Создать',
    Cancel: 'Отмена',
    nameNotEmpty: 'Имя не может быть пустым',
    appTemplateNotSelected: 'Пожалуйста, выберите шаблон',
    appTypeRequired: 'Пожалуйста, выберите тип приложения',
    appCreated: 'Приложение создано',
    appCreateFailed: 'Не удалось создать приложение',
    caution: 'Осторожность',
    appCreateDSLErrorPart2: 'Хотите продолжить?',
    Confirm: 'Подтверждать',
    appCreateDSLErrorTitle: 'Несовместимость версий',
    appCreateDSLErrorPart3: 'Актуальная версия приложения DSL:',
    appCreateDSLErrorPart4: 'Поддерживаемая системой версия DSL:',
    appCreateDSLWarning: 'Внимание: разница в версиях DSL может повлиять на некоторые функции',
    appCreateDSLErrorPart1: 'Обнаружена существенная разница в версиях DSL. Принудительный импорт может привести к сбою в работе приложения.',
    learnMore: 'Подробнее',
    forAdvanced: 'ДЛЯ ПРОДВИНУТЫХ ПОЛЬЗОВАТЕЛЕЙ',
    foundResults: '{{count}} Результаты',
    optional: 'Необязательный',
    chatbotShortDescription: 'Чат-бот на основе LLM с простой настройкой',
    advancedShortDescription: 'Рабочий процесс, улучшенный для многоходовых чатов',
    foundResult: '{{count}} Результат',
    workflowShortDescription: 'Агентный поток для интеллектуальных автоматизаций',
    advancedUserDescription: 'Рабочий процесс с дополнительными функциями памяти и интерфейсом чат-бота.',
    noAppsFound: 'Приложения не найдены',
    agentUserDescription: 'Интеллектуальный агент, способный к итеративным рассуждениям и автономному использованию инструментов для достижения целей задачи.',
    forBeginners: 'Более простые типы приложений',
    chatbotUserDescription: 'Быстро создайте чат-бота на основе LLM с простой настройкой. Вы можете переключиться на Chatflow позже.',
    noTemplateFound: 'Шаблоны не найдены',
    completionShortDescription: 'AI-помощник для задач генерации текста',
    noIdeaTip: 'Нет идей? Ознакомьтесь с нашими шаблонами',
    chooseAppType: 'Выберите тип приложения',
    agentShortDescription: 'Интеллектуальный агент с рассуждениями и автономным использованием инструментов',
    noTemplateFoundTip: 'Попробуйте искать по разным ключевым словам.',
    completionUserDescription: 'Быстро создайте помощника с искусственным интеллектом для задач генерации текста с простой настройкой.',
    workflowUserDescription: 'Визуально создавайте автономные ИИ-процессы простым перетаскиванием.',
    dropDSLToCreateApp: 'Перетащите файл DSL сюда, чтобы создать приложение',
  },
  editApp: 'Редактировать информацию',
  editAppTitle: 'Редактировать информацию о приложении',
  editDone: 'Информация о приложении обновлена',
  editFailed: 'Не удалось обновить информацию о приложении',
  iconPicker: {
    ok: 'ОК',
    cancel: 'Отмена',
    emoji: 'Эмодзи',
    image: 'Изображение',
  },
  switch: 'Переключиться на Workflow',
  switchTipStart: 'Для вас будет создана новая копия Workflow. Новая копия ',
  switchTip: 'не позволит',
  switchTipEnd: ' переключиться обратно на базовую организацию.',
  switchLabel: 'Копия приложения, которая будет создана',
  removeOriginal: 'Удалить исходное приложение',
  switchStart: 'Переключиться',
  typeSelector: {
    all: 'ВСЕ типы',
    chatbot: 'Чат-бот',
    agent: 'Агент',
    workflow: 'Рабочий процесс',
    completion: 'Завершение',
    advanced: 'Чатфлоу',
  },
  tracing: {
    title: 'Отслеживание производительности приложения',
    description: 'Настройка стороннего поставщика LLMOps и отслеживание производительности приложения.',
    config: 'Настройка',
    view: 'Просмотр',
    collapse: 'Свернуть',
    expand: 'Развернуть',
    tracing: 'Отслеживание',
    disabled: 'Отключено',
    disabledTip: 'Пожалуйста, сначала настройте провайдера LLM',
    enabled: 'В работе',
    tracingDescription: 'Запись полного контекста выполнения приложения, включая вызовы LLM, контекст, подсказки, HTTP-запросы и многое другое, на стороннюю платформу трассировки.',
    configProviderTitle: {
      configured: 'Настроено',
      notConfigured: 'Настройте провайдера, чтобы включить трассировку',
      moreProvider: 'Больше провайдеров',
    },
    arize: {
      title: 'Arize',
      description: 'Корпоративный уровень наблюдаемости LLM, онлайн и оффлайн оценка, мониторинг и эксперименты—на основе OpenTelemetry. Специально разработан для приложений на базе LLM и агентов.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Открытая и основанная на OpenTelemetry платформа для наблюдаемости, оценки, инженерии подсказок и экспериментов для ваших рабочих процессов и агентов LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Универсальная платформа для разработчиков для каждого этапа жизненного цикла приложения на базе LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Трассировка, оценка, управление подсказками и метрики для отладки и улучшения вашего приложения LLM.',
    },
    inUse: 'Используется',
    configProvider: {
      title: 'Настройка ',
      placeholder: 'Введите ваш {{key}}',
      project: 'Проект',
      publicKey: 'Публичный ключ',
      secretKey: 'Секретный ключ',
      viewDocsLink: 'Посмотреть документацию {{key}}',
      removeConfirmTitle: 'Удалить конфигурацию {{key}}?',
      removeConfirmContent: 'Текущая конфигурация используется, ее удаление отключит функцию трассировки.',
    },
    opik: {
      title: 'Опик',
      description: 'Opik — это платформа с открытым исходным кодом для оценки, тестирования и мониторинга LLM-приложений.',
    },
    weave: {
      description: 'Weave — это открытая платформа для оценки, тестирования и мониторинга приложений LLM.',
      title: 'Ткать',
    },
    aliyun: {
      title: 'Облачный монитор',
      description: 'Полностью управляемая и не требующая обслуживания платформа наблюдения, предоставляемая Alibaba Cloud, обеспечивает мониторинг, трассировку и оценку приложений Dify из коробки.',
    },
  },
  answerIcon: {
    title: 'Использование значка web app для замены 🤖',
    description: 'Следует ли использовать значок web app для замены 🤖 в общем приложении',
    descriptionInExplore: 'Следует ли использовать значок web app для замены 🤖 в разделе "Обзор"',
  },
  mermaid: {
    handDrawn: 'Рисованный',
    classic: 'Классический',
  },
  openInExplore: 'Открыть в разделе «Обзор»',
  newAppFromTemplate: {
    sidebar: {
      HR: 'ЧАС',
      Workflow: 'Рабочий процесс',
      Recommended: 'Рекомендованный',
      Agent: 'Агент',
      Assistant: 'Помощник',
      Writing: 'Пишущий',
      Programming: 'Программирование',
    },
    searchAllTemplate: 'Поиск по всем шаблонам...',
    byCategories: 'ПО КАТЕГОРИЯМ',
  },
  showMyCreatedAppsOnly: 'Показать только созданные мной приложения',
  appSelector: {
    label: 'ПРИЛОЖЕНИЕ',
    noParams: 'Параметры не нужны',
    placeholder: 'Выберите приложение...',
    params: 'ПАРАМЕТРЫ ПРИЛОЖЕНИЯ',
  },
  structOutput: {
    notConfiguredTip: 'Структурированный вывод еще не был настроен.',
    LLMResponse: 'Ответ LLM',
    structured: 'Структурированный',
    moreFillTip: 'Показано максимум 10 уровней вложенности',
    required: 'Необходимо',
    configure: 'Настроить',
    modelNotSupported: 'Модель не поддерживается',
    modelNotSupportedTip: 'Текущая модель не поддерживает эту функцию и автоматически понижается до инъекции подсказок.',
    structuredTip: 'Структурированные выходные данные — это функция, которая гарантирует, что модель всегда будет генерировать ответы, соответствующие вашей предоставленной JSON-схеме.',
  },
  accessItemsDescription: {
    anyone: 'Любой может получить доступ к веб-приложению',
    specific: 'Только определенные группы или участники могут получить доступ к веб-приложению.',
    organization: 'Любой в организации может получить доступ к веб-приложению',
    external: 'Только аутентифицированные внешние пользователи могут получить доступ к веб-приложению.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Кто угодно с ссылкой',
      specific: 'Конкретные группы или члены',
      organization: 'Только члены внутри предприятия',
      external: 'Аутентифицированные внешние пользователи',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Искать группы и участников',
      expand: 'Расширить',
      noResult: 'Нет результата',
      allMembers: 'Все члены',
    },
    title: 'Управление доступом к веб-приложению',
    description: 'Установите разрешения на доступ к веб-приложению',
    accessLabel: 'Кто имеет доступ',
    groups_one: '{{count}} ГРУППА',
    groups_other: '{{count}} ГРУПП',
    members_one: '{{count}} УЧАСТНИК',
    members_other: '{{count}} УЧАСТНИКИ',
    noGroupsOrMembers: 'Группы или участники не выбраны',
    updateSuccess: 'Обновление прошло успешно',
    webAppSSONotEnabledTip: 'Пожалуйста, свяжитесь с администратором предприятия, чтобы настроить метод аутентификации веб-приложения.',
  },
  publishApp: {
    title: 'Кто может получить доступ к веб-приложению',
    notSet: 'Не установлено',
    notSetDesc: 'В настоящее время никто не может получить доступ к веб-приложению. Пожалуйста, установите права доступа.',
  },
  accessControl: 'Управление доступом к веб-приложению',
  noAccessPermission: 'Нет разрешения на доступ к веб-приложению',
  maxActiveRequests: 'Максимальное количество параллельных запросов',
  maxActiveRequestsPlaceholder: 'Введите 0 для неограниченного количества',
}

export default translation
