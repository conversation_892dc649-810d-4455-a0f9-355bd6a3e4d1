const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON>',
    redo: '<PERSON><PERSON><PERSON>',
    editing: '<PERSON>and<PERSON>',
    autoSaved: 'Guardado automático',
    unpublished: 'No publicado',
    published: 'Publicado',
    publish: 'Publicar',
    update: 'Actualizar',
    run: 'Ejecutar',
    running: 'Ejecutando',
    inRunMode: 'En modo de ejecución',
    inPreview: 'En vista previa',
    inPreviewMode: 'En modo de vista previa',
    preview: 'Vista previa',
    viewRunHistory: 'Ver historial de ejecución',
    runHistory: 'Historial de ejecución',
    goBackToEdit: 'Vol<PERSON> al editor',
    conversationLog: 'Registro de conversación',
    features: 'Funcionalidades',
    debugAndPreview: 'Vista previa',
    restart: 'Reiniciar',
    currentDraft: 'Borrador actual',
    currentDraftUnpublished: 'Borrador actual no publicado',
    latestPublished: 'Último publicado',
    publishedAt: 'Publicado el',
    restore: 'Restaurar',
    runApp: 'Ejecutar aplicación',
    batchRunApp: 'Ejecutar aplicación en lote',
    accessAPIReference: 'Acceder a la referencia de la API',
    embedIntoSite: 'Insertar en el sitio',
    addTitle: 'Agregar título...',
    addDescription: 'Agregar descripción...',
    noVar: 'Sin variable',
    searchVar: 'Buscar variable',
    variableNamePlaceholder: 'Nombre de la variable',
    setVarValuePlaceholder: 'Establecer variable',
    needConnectTip: 'Este paso no está conectado a nada',
    maxTreeDepth: 'Límite máximo de {{depth}} nodos por rama',
    workflowProcess: 'Proceso de flujo de trabajo',
    notRunning: 'Aún no se está ejecutando',
    previewPlaceholder: 'Ingrese contenido en el cuadro de abajo para comenzar a depurar el Chatbot',
    effectVarConfirm: {
      title: 'Eliminar variable',
      content: 'La variable se utiliza en otros nodos. ¿Aún quieres eliminarla?',
    },
    insertVarTip: 'Presiona la tecla \'/\' para insertar rápidamente',
    processData: 'Procesar datos',
    input: 'Entrada',
    output: 'Salida',
    jinjaEditorPlaceholder: 'Escribe \'/\' o \'{\' para insertar una variable',
    viewOnly: 'Solo vista',
    showRunHistory: 'Mostrar historial de ejecución',
    enableJinja: 'Habilitar soporte de plantillas Jinja',
    learnMore: 'Más información',
    copy: 'Copiar',
    duplicate: 'Duplicar',
    pasteHere: 'Pegar aquí',
    pointerMode: 'Modo puntero',
    handMode: 'Modo mano',
    model: 'Modelo',
    workflowAsTool: 'Flujo de trabajo como herramienta',
    configureRequired: 'Configuración requerida',
    configure: 'Configurar',
    manageInTools: 'Administrar en Herramientas',
    workflowAsToolTip: 'Se requiere la reconfiguración de la herramienta después de la actualización del flujo de trabajo.',
    viewDetailInTracingPanel: 'Ver detalles',
    syncingData: 'Sincronizando datos, solo unos segundos.',
    importDSL: 'Importar DSL',
    importDSLTip: 'El borrador actual se sobrescribirá. Exporta el flujo de trabajo como respaldo antes de importar.',
    backupCurrentDraft: 'Respaldar borrador actual',
    chooseDSL: 'Elegir archivo DSL (yml)',
    overwriteAndImport: 'Sobrescribir e importar',
    importFailure: 'Error al importar',
    importSuccess: 'Importación exitosa',
    parallelTip: {
      click: {
        title: 'Clic',
        desc: 'Para agregar',
      },
      drag: {
        title: 'Arrastrar',
        desc: 'Para conectarse',
      },
      limit: 'El paralelismo se limita a {{num}} ramas.',
      depthLimit: 'Límite de capa de anidamiento paralelo de capas {{num}}',
    },
    parallelRun: 'Ejecución paralela',
    disconnect: 'Desconectar',
    jumpToNode: 'Saltar a este nodo',
    addParallelNode: 'Agregar nodo paralelo',
    parallel: 'PARALELO',
    branch: 'RAMA',
    fileUploadTip: 'Las funciones de carga de imágenes se han actualizado a la carga de archivos.',
    ImageUploadLegacyTip: 'Ahora puede crear variables de tipo de archivo en el formulario de inicio. Ya no admitiremos la función de carga de imágenes en el futuro.',
    featuresDescription: 'Mejorar la experiencia del usuario de la aplicación web',
    featuresDocLink: 'Aprende más',
    importWarning: 'Cautela',
    importWarningDetails: 'La diferencia de versión de DSL puede afectar a ciertas características',
    openInExplore: 'Abrir en Explorar',
    onFailure: 'Sobre el fracaso',
    addFailureBranch: 'Agregar rama de error',
    noHistory: 'Sin historia',
    loadMore: 'Cargar más flujos de trabajo',
    versionHistory: 'Historial de versiones',
    exportSVG: 'Exportar como SVG',
    exitVersions: 'Versiones de salida',
    exportJPEG: 'Exportar como JPEG',
    exportPNG: 'Exportar como PNG',
    referenceVar: 'Variable de referencia',
    publishUpdate: 'Publicar actualización',
    noExist: 'No existe tal variable',
    exportImage: 'Exportar imagen',
    needAnswerNode: 'Se debe agregar el nodo de respuesta',
    needEndNode: 'Se debe agregar el nodo Final',
    addBlock: 'Agregar nodo',
    tagBound: 'Número de aplicaciones que utilizan esta etiqueta',
  },
  env: {
    envPanelTitle: 'Variables de Entorno',
    envDescription: 'Las variables de entorno se pueden utilizar para almacenar información privada y credenciales. Son de solo lectura y se pueden separar del archivo DSL durante la exportación.',
    envPanelButton: 'Añadir Variable',
    modal: {
      title: 'Añadir Variable de Entorno',
      editTitle: 'Editar Variable de Entorno',
      type: 'Tipo',
      name: 'Nombre',
      namePlaceholder: 'nombre de env',
      value: 'Valor',
      valuePlaceholder: 'valor de env',
      secretTip: 'Se utiliza para definir información o datos sensibles, con configuraciones DSL configuradas para prevenir fugas.',
      description: 'Descripción',
      descriptionPlaceholder: 'Describa la variable',
    },
    export: {
      title: '¿Exportar variables de entorno secretas?',
      checkbox: 'Exportar valores secretos',
      ignore: 'Exportar DSL',
      export: 'Exportar DSL con valores secretos',
    },
  },
  chatVariable: {
    panelTitle: 'Variables de Conversación',
    panelDescription: 'Las Variables de Conversación se utilizan para almacenar información interactiva que el LLM necesita recordar, incluyendo el historial de conversación, archivos subidos y preferencias del usuario. Son de lectura y escritura.',
    docLink: 'Visite nuestra documentación para más información.',
    button: 'Añadir Variable',
    modal: {
      title: 'Añadir Variable de Conversación',
      editTitle: 'Editar Variable de Conversación',
      name: 'Nombre',
      namePlaceholder: 'Nombre de la variable',
      type: 'Tipo',
      value: 'Valor Predeterminado',
      valuePlaceholder: 'Valor predeterminado, dejar en blanco para no establecer',
      description: 'Descripción',
      descriptionPlaceholder: 'Describa la variable',
      editInJSON: 'Editar en JSON',
      oneByOne: 'Añadir uno por uno',
      editInForm: 'Editar en Formulario',
      arrayValue: 'Valor',
      addArrayValue: 'Añadir Valor',
      objectKey: 'Clave',
      objectType: 'Tipo',
      objectValue: 'Valor Predeterminado',
    },
    storedContent: 'Contenido almacenado',
    updatedAt: 'Actualizado el ',
  },
  changeHistory: {
    title: 'Historial de cambios',
    placeholder: 'Aún no has realizado cambios',
    clearHistory: 'Borrar historial',
    hint: 'Sugerencia',
    hintText: 'Tus acciones de edición se registran en un historial de cambios, que se almacena en tu dispositivo durante esta sesión. Este historial se borrará cuando salgas del editor.',
    stepBackward_one: '{{count}} paso hacia atrás',
    stepBackward_other: '{{count}} pasos hacia atrás',
    stepForward_one: '{{count}} paso hacia adelante',
    stepForward_other: '{{count}} pasos hacia adelante',
    sessionStart: 'Inicio de sesión',
    currentState: 'Estado actual',
    noteAdd: 'Nota agregada',
    noteChange: 'Nota cambiada',
    noteDelete: 'Nota eliminada',
    nodeTitleChange: 'Título del nodo cambiado',
    nodeAdd: 'Nodo añadido',
    nodePaste: 'Nodo pegado',
    nodeDragStop: 'Nodo movido',
    nodeConnect: 'Nodo conectado',
    edgeDelete: 'Nodo desconectado',
    nodeDelete: 'Nodo eliminado',
    nodeChange: 'Nodo cambiado',
    nodeDescriptionChange: 'Descripción del nodo cambiada',
    nodeResize: 'Nodo redimensionado',
  },
  errorMsg: {
    fieldRequired: 'Se requiere {{field}}',
    authRequired: 'Se requiere autorización',
    invalidJson: '{{field}} no es un JSON válido',
    fields: {
      variable: 'Nombre de la variable',
      variableValue: 'Valor de la variable',
      code: 'Código',
      model: 'Modelo',
      rerankModel: 'Modelo de reordenamiento',
      visionVariable: 'Variable de visión',
    },
    invalidVariable: 'Variable no válida',
    rerankModelRequired: 'Antes de activar el modelo de reclasificación, confirme que el modelo se ha configurado correctamente en la configuración.',
    toolParameterRequired: '{{campo}}: el parámetro [{{param}}] es obligatorio',
    noValidTool: '{{campo}} no se ha seleccionado ninguna herramienta válida',
  },
  singleRun: {
    testRun: 'Ejecución de prueba',
    startRun: 'Iniciar ejecución',
    running: 'Ejecutando',
    testRunIteration: 'Iteración de ejecución de prueba',
    back: 'Atrás',
    iteration: 'Iteración',
    loop: 'Bucle',
  },
  tabs: {
    'tools': 'Herramientas',
    'allTool': 'Todos',
    'builtInTool': 'Incorporadas',
    'customTool': 'Personalizadas',
    'workflowTool': 'Flujo de trabajo',
    'question-understand': 'Entender pregunta',
    'logic': 'Lógica',
    'transform': 'Transformar',
    'utilities': 'Utilidades',
    'noResult': 'No se encontraron coincidencias',
    'searchTool': 'Herramienta de búsqueda',
    'agent': 'Estrategia del agente',
    'plugin': 'Plugin',
    'searchBlock': 'Buscar nodo',
    'blocks': 'Nodos',
    'addAll': 'Agregar todo',
    'allAdded': 'Todo añadido',
  },
  blocks: {
    'start': 'Inicio',
    'end': 'Fin',
    'answer': 'Respuesta',
    'llm': 'LLM',
    'knowledge-retrieval': 'Recuperación de conocimiento',
    'question-classifier': 'Clasificador de preguntas',
    'if-else': 'SI/SINO',
    'code': 'Código',
    'template-transform': 'Plantilla',
    'http-request': 'Solicitud HTTP',
    'variable-assigner': 'Asignador de variables',
    'variable-aggregator': 'Agregador de variables',
    'assigner': 'Asignador de Variables',
    'iteration-start': 'Inicio de iteración',
    'iteration': 'Iteración',
    'parameter-extractor': 'Extractor de parámetros',
    'document-extractor': 'Extractor de documentos',
    'list-operator': 'Operador de lista',
    'agent': 'Agente',
    'loop-end': 'Salir del bucle',
    'loop': 'Bucle',
    'loop-start': 'Inicio del bucle',
  },
  blocksAbout: {
    'start': 'Define los parámetros iniciales para iniciar un flujo de trabajo',
    'end': 'Define el final y el tipo de resultado de un flujo de trabajo',
    'answer': 'Define el contenido de respuesta de una conversación de chat',
    'llm': 'Invoca modelos de lenguaje grandes para responder preguntas o procesar lenguaje natural',
    'knowledge-retrieval': 'Te permite consultar contenido de texto relacionado con las preguntas de los usuarios desde el conocimiento',
    'question-classifier': 'Define las condiciones de clasificación de las preguntas de los usuarios, LLM puede definir cómo progresa la conversación en función de la descripción de clasificación',
    'if-else': 'Te permite dividir el flujo de trabajo en dos ramas basadas en condiciones SI/SINO',
    'code': 'Ejecuta un fragmento de código Python o NodeJS para implementar lógica personalizada',
    'template-transform': 'Convierte datos en una cadena utilizando la sintaxis de plantillas Jinja',
    'http-request': 'Permite enviar solicitudes al servidor a través del protocolo HTTP',
    'variable-assigner': 'Agrega variables de múltiples ramas en una sola variable para configurar de manera unificada los nodos descendentes.',
    'assigner': 'El nodo de asignación de variables se utiliza para asignar valores a variables escribibles (como variables de conversación).',
    'variable-aggregator': 'Agrega variables de múltiples ramas en una sola variable para configurar de manera unificada los nodos descendentes.',
    'iteration': 'Realiza múltiples pasos en un objeto de lista hasta que se generen todos los resultados.',
    'parameter-extractor': 'Utiliza LLM para extraer parámetros estructurados del lenguaje natural para invocaciones de herramientas o solicitudes HTTP.',
    'list-operator': 'Se utiliza para filtrar u ordenar el contenido de la matriz.',
    'document-extractor': 'Se utiliza para analizar documentos cargados en contenido de texto que es fácilmente comprensible por LLM.',
    'agent': 'Invocar modelos de lenguaje de gran tamaño para responder preguntas o procesar el lenguaje natural',
    'loop-end': 'Equivalente a "romper". Este nodo no tiene elementos de configuración. Cuando el cuerpo del bucle alcanza este nodo, el bucle termina.',
    'loop': 'Ejecuta un bucle de lógica hasta que se cumpla la condición de terminación o se alcance el conteo máximo de bucles.',
  },
  operator: {
    zoomIn: 'Acercar',
    zoomOut: 'Alejar',
    zoomTo50: 'Zoom al 50%',
    zoomTo100: 'Zoom al 100%',
    zoomToFit: 'Ajustar al tamaño',
  },
  panel: {
    userInputField: 'Campo de entrada del usuario',
    helpLink: 'Enlace de ayuda',
    about: 'Acerca de',
    createdBy: 'Creado por ',
    nextStep: 'Siguiente paso',
    runThisStep: 'Ejecutar este paso',
    checklist: 'Lista de verificación',
    checklistTip: 'Asegúrate de resolver todos los problemas antes de publicar',
    checklistResolved: 'Se resolvieron todos los problemas',
    change: 'Cambiar',
    optional: '(opcional)',
    moveToThisNode: 'Mueve a este nodo',
    organizeBlocks: 'Organizar nodos',
    addNextStep: 'Agrega el siguiente paso en este flujo de trabajo',
    changeBlock: 'Cambiar Nodo',
    selectNextStep: 'Seleccionar siguiente paso',
    maximize: 'Maximizar Canvas',
    minimize: 'Salir de pantalla completa',
  },
  nodes: {
    common: {
      outputVars: 'Variables de salida',
      insertVarTip: 'Insertar variable',
      memory: {
        memory: 'Memoria',
        memoryTip: 'Configuración de memoria de chat',
        windowSize: 'Tamaño de ventana',
        conversationRoleName: 'Nombre del rol de conversación',
        user: 'Prefijo de usuario',
        assistant: 'Prefijo de asistente',
      },
      memories: {
        title: 'Memorias',
        tip: 'Memoria de chat',
        builtIn: 'Incorporada',
      },
      errorHandle: {
        none: {
          title: 'Ninguno',
          desc: 'El nodo dejará de ejecutarse si se produce una excepción y no se controla',
        },
        defaultValue: {
          title: 'Valor predeterminado',
          desc: 'Cuando se produzca un error, especifique un contenido de salida estático.',
          tip: 'En caso de error, devolverá un valor inferior.',
          inLog: 'Excepción de nodo, salida según los valores predeterminados.',
          output: 'Valor predeterminado de salida',
        },
        failBranch: {
          title: 'Rama de error',
          desc: 'Cuando se produce un error, ejecutará la rama de excepción',
          customize: 'Vaya al lienzo para personalizar la lógica de la rama de error.',
          customizeTip: 'Cuando se activa la rama fail, las excepciones lanzadas por los nodos no finalizarán el proceso. En su lugar, ejecutará automáticamente la rama de error predefinida, lo que le permitirá proporcionar de forma flexible mensajes de error, informes, correcciones u omitir acciones.',
          inLog: 'Node, ejecutará automáticamente la rama de error. La salida del nodo devolverá un tipo de error y un mensaje de error y los pasará a la versión posterior.',
        },
        partialSucceeded: {
          tip: 'Hay nodos {{num}} en el proceso que se ejecutan de manera anormal, vaya a rastreo para verificar los registros.',
        },
        title: 'Manejo de errores',
        tip: 'Estrategia de control de excepciones, que se desencadena cuando un nodo encuentra una excepción.',
      },
      retry: {
        retryOnFailure: 'Volver a intentarlo en caso de error',
        maxRetries: 'Número máximo de reintentos',
        retryInterval: 'Intervalo de reintento',
        retryTimes: 'Reintentar {{times}} veces en caso de error',
        retrying: 'Reintentando...',
        retrySuccessful: 'Volver a intentarlo correctamente',
        retryFailed: 'Error en el reintento',
        retryFailedTimes: '{{veces}} reintentos fallidos',
        times: 'veces',
        ms: 'Sra.',
        retries: '{{num}} Reintentos',
        retry: 'Reintentar',
      },
      typeSwitch: {
        input: 'Valor de entrada',
        variable: 'Usa la variable',
      },
    },
    start: {
      required: 'requerido',
      inputField: 'Campo de entrada',
      builtInVar: 'Variables incorporadas',
      outputVars: {
        query: 'Entrada del usuario',
        memories: {
          des: 'Historial de conversación',
          type: 'tipo de mensaje',
          content: 'contenido del mensaje',
        },
        files: 'Lista de archivos',
      },
      noVarTip: 'Establece las entradas que se pueden utilizar en el flujo de trabajo',
    },
    end: {
      outputs: 'Salidas',
      output: {
        type: 'tipo de salida',
        variable: 'variable de salida',
      },
      type: {
        'none': 'Ninguno',
        'plain-text': 'Texto sin formato',
        'structured': 'Estructurado',
      },
    },
    answer: {
      answer: 'Respuesta',
      outputVars: 'Variables de salida',
    },
    llm: {
      model: 'modelo',
      variables: 'variables',
      context: 'contexto',
      contextTooltip: 'Puedes importar el conocimiento como contexto',
      notSetContextInPromptTip: 'Para habilitar la función de contexto, completa la variable de contexto en PROMPT.',
      prompt: 'indicación',
      roleDescription: {
        system: 'Proporciona instrucciones generales para la conversación',
        user: 'Proporciona instrucciones, consultas o cualquier entrada basada en texto al modelo',
        assistant: 'Las respuestas del modelo basadas en los mensajes del usuario',
      },
      addMessage: 'Agregar mensaje',
      vision: 'visión',
      files: 'Archivos',
      resolution: {
        name: 'Resolución',
        high: 'Alta',
        low: 'Baja',
      },
      outputVars: {
        output: 'Generar contenido',
        usage: 'Información de uso del modelo',
      },
      singleRun: {
        variable: 'Variable',
      },
      sysQueryInUser: 'se requiere sys.query en el mensaje del usuario',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Por favor, termina de editar el campo actual antes de guardar el esquema.',
        },
        showAdvancedOptions: 'Mostrar opciones avanzadas',
        addField: 'Agregar campo',
        generatedResult: 'Resultado Generado',
        generateJsonSchema: 'Generar esquema JSON',
        apply: 'Aplicar',
        descriptionPlaceholder: 'Agregar descripción',
        stringValidations: 'Validaciones de cadenas',
        addChildField: 'Agregar campo de niño',
        back: 'Atrás',
        promptTooltip: 'Convierta la descripción del texto en una estructura de esquema JSON estandarizada.',
        doc: 'Aprender más sobre la salida estructurada',
        generating: 'Generando esquema JSON...',
        fieldNamePlaceholder: 'Nombre del campo',
        resultTip: 'Aquí está el resultado generado. Si no estás satisfecho, puedes regresar y modificar tu solicitud.',
        title: 'Esquema de salida estructurada',
        regenerate: 'Regenerar',
        instruction: 'Instrucción',
        generationTip: 'Puedes usar lenguaje natural para crear rápidamente un esquema JSON.',
        promptPlaceholder: 'Describe tu esquema JSON...',
        required: 'requerido',
        generate: 'Generar',
        import: 'Importar desde JSON',
        resetDefaults: 'Restablecer',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Variable de consulta',
      knowledge: 'Conocimiento',
      outputVars: {
        output: 'Datos segmentados de recuperación',
        content: 'Contenido segmentado',
        title: 'Título segmentado',
        icon: 'Ícono segmentado',
        url: 'URL segmentada',
        metadata: 'Metadatos adicionales',
      },
      metadata: {
        options: {
          disabled: {
            subTitle: 'No habilitar el filtrado de metadatos',
            title: 'Deshabilitado',
          },
          automatic: {
            subTitle: 'Generar automáticamente condiciones de filtrado de metadatos basadas en la consulta del usuario',
            desc: 'Generar automáticamente condiciones de filtrado de metadatos basadas en la variable de consulta',
            title: 'Automático',
          },
          manual: {
            title: 'Manual',
            subTitle: 'Añadir manualmente condiciones de filtro de metadatos',
          },
        },
        panel: {
          conditions: 'Condiciones',
          title: 'Condiciones del filtro de metadatos',
          add: 'Agregar condición',
          select: 'Seleccionar variable...',
          datePlaceholder: 'Elige una hora...',
          placeholder: 'Ingrese valor',
          search: 'Buscar metadatos',
        },
        title: 'Filtrado de Metadatos',
      },
    },
    http: {
      inputVars: 'Variables de entrada',
      api: 'API',
      apiPlaceholder: 'Ingresa la URL, escribe \'/\' para insertar una variable',
      notStartWithHttp: 'La API debe comenzar con http:// o https://',
      key: 'Clave',
      value: 'Valor',
      bulkEdit: 'Edición masiva',
      keyValueEdit: 'Edición clave-valor',
      headers: 'Encabezados',
      params: 'Parámetros',
      body: 'Cuerpo',
      outputVars: {
        body: 'Contenido de la respuesta',
        statusCode: 'Código de estado de la respuesta',
        headers: 'Lista de encabezados de respuesta en formato JSON',
        files: 'Lista de archivos',
      },
      authorization: {
        'authorization': 'Autorización',
        'authorizationType': 'Tipo de autorización',
        'no-auth': 'Ninguna',
        'api-key': 'Clave de API',
        'auth-type': 'Tipo de autenticación',
        'basic': 'Básica',
        'bearer': 'Bearer',
        'custom': 'Personalizada',
        'api-key-title': 'Clave de API',
        'header': 'Encabezado',
      },
      insertVarPlaceholder: 'escribe \'/\' para insertar una variable',
      timeout: {
        title: 'Tiempo de espera',
        connectLabel: 'Tiempo de espera de conexión',
        connectPlaceholder: 'Ingresa el tiempo de espera de conexión en segundos',
        readLabel: 'Tiempo de espera de lectura',
        readPlaceholder: 'Ingresa el tiempo de espera de lectura en segundos',
        writeLabel: 'Tiempo de espera de escritura',
        writePlaceholder: 'Ingresa el tiempo de espera de escritura en segundos',
      },
      type: 'Tipo',
      binaryFileVariable: 'Variable de archivo binario',
      extractListPlaceholder: 'Introduzca el índice de elementos de la lista, escriba \'/\' insertar variable',
      curl: {
        title: 'Importar desde cURL',
        placeholder: 'Pegar la cadena cURL aquí',
      },
      verifySSL: {
        title: 'Verificar el certificado SSL',
        warningTooltip: 'Deshabilitar la verificación SSL no se recomienda para entornos de producción. Esto solo debe utilizarse en desarrollo o pruebas, ya que hace que la conexión sea vulnerable a amenazas de seguridad como ataques de intermediario.',
      },
    },
    code: {
      inputVars: 'Variables de entrada',
      outputVars: 'Variables de salida',
      advancedDependencies: 'Dependencias avanzadas',
      advancedDependenciesTip: 'Agrega algunas dependencias precargadas que consumen más tiempo o no son incorporadas por defecto aquí',
      searchDependencies: 'Buscar dependencias',
      syncFunctionSignature: 'Sincronizar la firma de la función con el código',
    },
    templateTransform: {
      inputVars: 'Variables de entrada',
      code: 'Código',
      codeSupportTip: 'Solo admite Jinja2',
      outputVars: {
        output: 'Contenido transformado',
      },
    },
    ifElse: {
      if: 'Si',
      else: 'Sino',
      elseDescription: 'Se utiliza para definir la lógica que se debe ejecutar cuando no se cumple la condición del si.',
      and: 'y',
      or: 'o',
      operator: 'Operador',
      notSetVariable: 'Por favor, establece primero la variable',
      comparisonOperator: {
        'contains': 'contiene',
        'not contains': 'no contiene',
        'start with': 'comienza con',
        'end with': 'termina con',
        'is': 'es',
        'is not': 'no es',
        'empty': 'está vacío',
        'not empty': 'no está vacío',
        'null': 'es nulo',
        'not null': 'no es nulo',
        'regex match': 'Coincidencia de expresiones regulares',
        'not in': 'no en',
        'in': 'en',
        'exists': 'Existe',
        'all of': 'Todos los',
        'not exists': 'no existe',
        'after': 'después',
        'before': 'antes',
      },
      enterValue: 'Ingresa un valor',
      addCondition: 'Agregar condición',
      conditionNotSetup: 'Condición NO configurada',
      selectVariable: 'Seleccionar variable...',
      optionName: {
        audio: 'Audio',
        image: 'Imagen',
        doc: 'Doc',
        localUpload: 'Carga local',
        video: 'Vídeo',
        url: 'URL',
      },
      select: 'Escoger',
      addSubVariable: 'Sub Variable',
      condition: 'Condición',
    },
    variableAssigner: {
      title: 'Asignar variables',
      outputType: 'Tipo de salida',
      varNotSet: 'Variable no establecida',
      noVarTip: 'Agrega las variables que se asignarán',
      type: {
        string: 'Cadena',
        number: 'Número',
        object: 'Objeto',
        array: 'Arreglo',
      },
      aggregationGroup: 'Grupo de agregación',
      aggregationGroupTip: 'Al habilitar esta función, el agregador de variables puede agregar múltiples conjuntos de variables.',
      addGroup: 'Agregar grupo',
      outputVars: {
        varDescribe: 'Salida de {{groupName}}',
      },
      setAssignVariable: 'Establecer variable asignada',
    },
    assigner: {
      'assignedVariable': 'Variable Asignada',
      'writeMode': 'Modo de Escritura',
      'writeModeTip': 'Cuando la VARIABLE ASIGNADA es un array, el modo de anexar agrega al final.',
      'over-write': 'Sobrescribir',
      'append': 'Anexar',
      'plus': 'Más',
      'clear': 'Limpiar',
      'setVariable': 'Establecer Variable',
      'variable': 'Variable',
      'operations': {
        'clear': 'Claro',
        '*=': '*=',
        '-=': '-=',
        'title': 'Operación',
        'extend': 'Extender',
        'append': 'Añadir',
        '+=': '+=',
        'over-write': 'Sobrescribir',
        'overwrite': 'Sobrescribir',
        '/=': '/=',
        'set': 'Poner',
        'remove-last': 'Eliminar último',
        'remove-first': 'Eliminar primero',
      },
      'variables': 'Variables',
      'setParameter': 'Establecer parámetro...',
      'noVarTip': 'Haga clic en el botón "+" para agregar variables',
      'varNotSet': 'Variable NO establecida',
      'noAssignedVars': 'No hay variables asignadas disponibles',
      'selectAssignedVariable': 'Seleccione la variable asignada...',
      'assignedVarsDescription': 'Las variables asignadas deben ser variables grabables, como las variables de conversación.',
    },
    tool: {
      inputVars: 'Variables de entrada',
      outputVars: {
        text: 'Contenido generado por la herramienta',
        files: {
          title: 'Archivos generados por la herramienta',
          type: 'Tipo de soporte. Ahora solo admite imágenes',
          transfer_method: 'Método de transferencia. El valor es remote_url o local_file',
          url: 'URL de la imagen',
          upload_file_id: 'ID de archivo cargado',
        },
        json: 'JSON generado por la herramienta',
      },
      authorize: 'autorizar',
      insertPlaceholder2: 'insertar variable',
      settings: 'Ajustes',
      insertPlaceholder1: 'Escribe o presiona',
    },
    questionClassifiers: {
      model: 'modelo',
      inputVars: 'Variables de entrada',
      outputVars: {
        className: 'Nombre de la clase',
        usage: 'Información de uso del modelo',
      },
      class: 'Clase',
      classNamePlaceholder: 'Escribe el nombre de tu clase',
      advancedSetting: 'Configuración avanzada',
      topicName: 'Nombre del tema',
      topicPlaceholder: 'Escribe el nombre de tu tema',
      addClass: 'Agregar clase',
      instruction: 'Instrucción',
      instructionTip: 'Input additional instructions to help the question classifier better understand how to categorize questions.',
      instructionPlaceholder: 'Write your instruction',
    },
    parameterExtractor: {
      inputVar: 'Variable de entrada',
      outputVars: {
        isSuccess: 'Es éxito. En caso de éxito el valor es 1, en caso de fallo el valor es 0.',
        errorReason: 'Motivo del error',
        usage: 'Información de uso del modelo',
      },
      extractParameters: 'Extraer parámetros',
      importFromTool: 'Importar desde herramientas',
      addExtractParameter: 'Agregar parámetro de extracción',
      addExtractParameterContent: {
        name: 'Nombre',
        namePlaceholder: 'Nombre del parámetro de extracción',
        type: 'Tipo',
        typePlaceholder: 'Tipo de parámetro de extracción',
        description: 'Descripción',
        descriptionPlaceholder: 'Descripción del parámetro de extracción',
        required: 'Requerido',
        requiredContent: 'El campo requerido se utiliza solo como referencia para la inferencia del modelo, y no para la validación obligatoria de la salida del parámetro.',
      },
      extractParametersNotSet: 'Parámetros de extracción no configurados',
      instruction: 'Instrucción',
      instructionTip: 'Ingrese instrucciones adicionales para ayudar al extractor de parámetros a entender cómo extraer parámetros.',
      advancedSetting: 'Configuración avanzada',
      reasoningMode: 'Modo de razonamiento',
      reasoningModeTip: 'Puede elegir el modo de razonamiento apropiado basado en la capacidad del modelo para responder a instrucciones para llamadas de funciones o indicaciones.',
    },
    iteration: {
      deleteTitle: '¿Eliminar nodo de iteración?',
      deleteDesc: 'Eliminar el nodo de iteración eliminará todos los nodos secundarios',
      input: 'Entrada',
      output: 'Variables de salida',
      iteration_one: '{{count}} Iteración',
      iteration_other: '{{count}} Iteraciones',
      currentIteration: 'Iteración actual',
      ErrorMethod: {
        operationTerminated: 'Terminado',
        continueOnError: 'Continuar en el error',
        removeAbnormalOutput: 'eliminar-salida-anormal',
      },
      comma: ',',
      errorResponseMethod: 'Método de respuesta a errores',
      error_one: '{{conteo}} Error',
      parallelPanelDesc: 'En el modo paralelo, las tareas de la iteración admiten la ejecución en paralelo.',
      MaxParallelismTitle: 'Máximo paralelismo',
      error_other: '{{conteo}} Errores',
      parallelMode: 'Modo paralelo',
      parallelModeEnableDesc: 'En el modo paralelo, las tareas dentro de las iteraciones admiten la ejecución en paralelo. Puede configurar esto en el panel de propiedades a la derecha.',
      parallelModeUpper: 'MODO PARALELO',
      MaxParallelismDesc: 'El paralelismo máximo se utiliza para controlar el número de tareas ejecutadas simultáneamente en una sola iteración.',
      answerNodeWarningDesc: 'Advertencia de modo paralelo: Los nodos de respuesta, las asignaciones de variables de conversación y las operaciones de lectura/escritura persistentes dentro de las iteraciones pueden provocar excepciones.',
      parallelModeEnableTitle: 'Modo paralelo habilitado',
    },
    note: {
      addNote: 'Agregar nota',
      editor: {
        placeholder: 'Escribe tu nota...',
        small: 'Pequeño',
        medium: 'Mediano',
        large: 'Grande',
        bold: 'Negrita',
        italic: 'Itálica',
        strikethrough: 'Tachado',
        link: 'Enlace',
        openLink: 'Abrir',
        unlink: 'Quitar enlace',
        enterUrl: 'Introducir URL...',
        invalidUrl: 'URL inválida',
        bulletList: 'Lista de viñetas',
        showAuthor: 'Mostrar autor',
      },
    },
    tracing: {
      stopBy: 'Detenido por {{user}}',
    },
    docExtractor: {
      outputVars: {
        text: 'Texto extraído',
      },
      learnMore: 'Aprende más',
      supportFileTypes: 'Tipos de archivos de soporte: {{tipos}}.',
      inputVar: 'Variable de entrada',
    },
    listFilter: {
      outputVars: {
        first_record: 'Primer registro',
        last_record: 'Último registro',
        result: 'Filtrar resultado',
      },
      filterCondition: 'Condición del filtro',
      filterConditionComparisonValue: 'Valor de la condición de filtro',
      inputVar: 'Variable de entrada',
      desc: 'DESC',
      limit: 'Arriba N',
      filterConditionKey: 'Clave de condición de filtro',
      orderBy: 'Ordenar por',
      filterConditionComparisonOperator: 'Operador de comparación de condiciones de filtro',
      asc: 'ASC',
      selectVariableKeyPlaceholder: 'Seleccione la clave de subvariable',
      extractsCondition: 'Extraiga el elemento N',
    },
    agent: {
      strategy: {
        configureTip: 'Configure la estrategia de agentes.',
        tooltip: 'Diferentes estrategias agentic determinan cómo el sistema planifica y ejecuta las llamadas a herramientas de varios pasos',
        label: 'Estrategia Agentica',
        shortLabel: 'Estrategia',
        configureTipDesc: 'Después de configurar la estrategia agentica, este nodo cargará automáticamente las configuraciones restantes. La estrategia afectará el mecanismo de razonamiento de herramientas de varios pasos.',
        selectTip: 'Seleccionar estrategia agentica',
        searchPlaceholder: 'Estrategia de agentes de búsqueda',
      },
      pluginInstaller: {
        install: 'Instalar',
        installing: 'Instalar',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'Administrar en Plugins',
        desc: 'Este modelo se instala desde el repositorio local o de GitHub. Úselo después de la instalación.',
        title: 'Modelo no instalado',
      },
      modelNotSupport: {
        descForVersionSwitch: 'La versión del plugin instalado no proporciona este modelo. Haga clic para cambiar de versión.',
        desc: 'La versión del plugin instalado no proporciona este modelo.',
        title: 'Modelo no compatible',
      },
      modelSelectorTooltips: {
        deprecated: 'Este modelo está en desuso',
      },
      outputVars: {
        files: {
          url: 'URL de la imagen',
          title: 'Archivos generados por el agente',
          upload_file_id: 'Cargar ID de archivo',
          transfer_method: 'Método de transferencia. El valor es remote_url o local_file',
          type: 'Tipo de soporte. Ahora solo admite imagen',
        },
        json: 'JSON generado por el agente',
        text: 'Contenido generado por el agente',
      },
      checkList: {
        strategyNotSelected: 'Estrategia no seleccionada',
      },
      installPlugin: {
        install: 'Instalar',
        desc: 'A punto de instalar el siguiente plugin',
        changelog: 'Registro de cambios',
        title: 'Instalar plugin',
        cancel: 'Cancelar',
      },
      tools: 'Herramientas',
      pluginNotFoundDesc: 'Este plugin se instala desde GitHub. Por favor, vaya a Plugins para reinstalar',
      strategyNotFoundDesc: 'La versión del plugin instalado no proporciona esta estrategia.',
      strategyNotInstallTooltip: '{{estrategia}} no está instalado',
      modelNotInstallTooltip: 'Este modelo no está instalado',
      maxIterations: 'Iteraciones máximas',
      notAuthorized: 'No autorizado',
      toolNotInstallTooltip: '{{herramienta}} no está instalada',
      toolbox: 'caja de herramientas',
      strategyNotSet: 'Estrategia agentica No establecida',
      unsupportedStrategy: 'Estrategia no respaldada',
      linkToPlugin: 'Enlace a los plugins',
      learnMore: 'Aprende más',
      configureModel: 'Configurar modelo',
      pluginNotInstalled: 'Este plugin no está instalado',
      model: 'modelo',
      pluginNotInstalledDesc: 'Este plugin se instala desde GitHub. Por favor, vaya a Plugins para reinstalar',
      strategyNotFoundDescAndSwitchVersion: 'La versión del plugin instalado no proporciona esta estrategia. Haga clic para cambiar de versión.',
      toolNotAuthorizedTooltip: '{{herramienta}} No autorizado',
      modelNotSelected: 'Modelo no seleccionado',
      clickToViewParameterSchema: 'Haga clic para ver el esquema de parámetros',
      parameterSchema: 'Esquema de Parámetros',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'Eliminar salida anormal',
        operationTerminated: 'Terminado',
        continueOnError: 'Continuar con el error',
      },
      loopMaxCount: 'Conteo máximo de bucles',
      output: 'Variable de Salida',
      currentLoopCount: 'Contador de bucles actual: {{count}}',
      currentLoop: 'Bucle de corriente',
      loopNode: 'Nodo de bucle',
      deleteDesc: 'Eliminar el nodo de bucle eliminará todos los nodos hijos',
      totalLoopCount: 'Total de loops: {{count}}',
      comma: ',',
      finalLoopVariables: 'Variables del Bucle Final',
      inputMode: 'Modo de entrada',
      deleteTitle: '¿Eliminar nodo de bucle?',
      setLoopVariables: 'Establecer variables dentro del alcance del bucle',
      loop_other: '{{count}} bucles',
      breakCondition: 'Condición de terminación del bucle',
      loopMaxCountError: 'Por favor, introduce un conteo máximo de bucles válido, que varíe entre 1 y {{maxCount}}.',
      exitConditionTip: 'Un nodo de bucle necesita al menos una condición de salida',
      error_one: '{{count}} Error',
      loop_one: '{{count}} Bucle',
      initialLoopVariables: 'Variables de Bucle Iniciales',
      errorResponseMethod: 'Método de respuesta de error',
      breakConditionTip: 'Solo se pueden hacer referencia a las variables dentro de bucles con condiciones de terminación y variables de conversación.',
      error_other: '{{count}} Errores',
      loopVariables: 'Variables de bucle',
      variableName: 'Nombre de Variable',
      input: 'Entrada',
    },
  },
  tracing: {
    stopBy: 'Pásate por {{usuario}}',
  },
  variableReference: {
    noAvailableVars: 'No hay variables disponibles',
    assignedVarsDescription: 'Las variables asignadas deben ser variables grabables, como',
    noVarsForOperation: 'No hay variables disponibles para la asignación con la operación seleccionada.',
    noAssignedVars: 'No hay variables asignadas disponibles',
    conversationVars: 'Variables de conversación',
  },
  versionHistory: {
    filter: {
      onlyYours: 'Solo tuyo',
      onlyShowNamedVersions: 'Solo muestra versiones nombradas',
      empty: 'No se encontró un historial de versiones coincidente.',
      reset: 'Restablecer filtro',
      all: 'Todo',
    },
    editField: {
      titleLengthLimit: 'El título no puede exceder {{limit}} caracteres',
      title: 'Título',
      releaseNotesLengthLimit: 'Las notas de lanzamiento no pueden exceder {{limit}} caracteres',
      releaseNotes: 'Notas de Lanzamiento',
    },
    action: {
      deleteSuccess: 'Versión eliminada',
      updateSuccess: 'Versión actualizada',
      restoreFailure: 'Error al restaurar la versión',
      deleteFailure: 'Error al eliminar la versión',
      updateFailure: 'Error al actualizar la versión',
      restoreSuccess: 'Versión restaurada',
    },
    releaseNotesPlaceholder: 'Describe lo que cambió',
    restorationTip: 'Después de la restauración de la versión, el borrador actual será sobrescrito.',
    nameThisVersion: 'Nombra esta versión',
    defaultName: 'Versión sin título',
    title: 'Versiones',
    deletionTip: 'La eliminación es irreversible, por favor confirma.',
    currentDraft: 'Borrador Actual',
    editVersionInfo: 'Editar información de la versión',
    latest: 'Último',
  },
  debug: {
    noData: {
      runThisNode: 'Ejecuta este nodo',
      description: 'Los resultados de la última ejecución se mostrarán aquí',
    },
    variableInspect: {
      trigger: {
        running: 'Estado de ejecución de la caché',
        stop: 'Detén la carrera',
        normal: 'Inspeccionar Variable',
        cached: 'Ver variables en caché',
        clear: 'Claro',
      },
      envNode: 'Medio ambiente',
      chatNode: 'Conversación',
      systemNode: 'Sistema',
      view: 'Ver registro',
      clearAll: 'Restablecer todo',
      emptyLink: 'Aprender más',
      title: 'Inspeccionar Variable',
      reset: 'Restablecer al último valor ejecutado',
      resetConversationVar: 'Restablecer la variable de conversación al valor predeterminado',
      clearNode: 'Limpiar variable en caché',
      emptyTip: 'Después de recorrer un nodo en el lienzo o ejecutar un nodo paso a paso, puedes ver el valor actual de la variable del nodo en Inspección de Variables.',
      edited: 'Editado',
    },
    lastRunTab: 'Última ejecución',
    settingsTab: 'Ajustes',
  },
}

export default translation
