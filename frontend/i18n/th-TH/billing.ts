const translation = {
  currentPlan: 'แผนปัจจุบัน',
  upgradeBtn: {
    plain: 'แผนการอัปเกรด',
    encourage: 'อัปเกรดเดี๋ยวนี้',
    encourageShort: 'อัพ เกรด',
  },
  viewBilling: 'จัดการการเรียกเก็บเงินและการสมัครใช้งาน',
  buyPermissionDeniedTip: 'โปรดติดต่อผู้ดูแลระบบองค์กรของคุณเพื่อสมัครสมาชิก',
  plansCommon: {
    title: 'เลือกแผนบริการที่เหมาะกับคุณ',
    yearlyTip: 'รับฟรี 2 เดือนโดยสมัครสมาชิกรายปี!',
    mostPopular: 'แห่ง',
    planRange: {
      monthly: 'รายเดือน',
      yearly: 'รายปี',
    },
    month: 'เดือน',
    year: 'ปี',
    save: 'ประหยัด',
    free: 'ฟรี',
    currentPlan: 'แผนปัจจุบัน',
    contractSales: 'ติดต่อฝ่ายขาย',
    contractOwner: 'ติดต่อผู้จัดการทีม',
    startForFree: 'เริ่มฟรี',
    getStartedWith: 'เริ่มต้นใช้งาน',
    contactSales: 'ติดต่อฝ่ายขาย',
    talkToSales: 'พูดคุยกับฝ่ายขาย',
    modelProviders: 'ผู้ให้บริการโมเดล',
    teamMembers: 'สมาชิกในทีม',
    annotationQuota: 'โควต้าคําอธิบายประกอบ',
    buildApps: 'สร้างแอพ',
    vectorSpace: 'พื้นที่เวกเตอร์',
    vectorSpaceBillingTooltip: 'แต่ละ 1MB สามารถจัดเก็บข้อมูลแบบเวกเตอร์ได้ประมาณ 1.2 ล้านอักขระ (โดยประมาณโดยใช้ OpenAI Embeddings แตกต่างกันไปตามรุ่น)',
    vectorSpaceTooltip: 'Vector Space เป็นระบบหน่วยความจําระยะยาวที่จําเป็นสําหรับ LLM ในการทําความเข้าใจข้อมูลของคุณ',
    documentsUploadQuota: 'โควต้าการอัปโหลดเอกสาร',
    documentProcessingPriority: 'ลําดับความสําคัญในการประมวลผลเอกสาร',
    documentProcessingPriorityTip: 'สําหรับลําดับความสําคัญในการประมวลผลเอกสารที่สูงขึ้น โปรดอัปเกรดแผนของคุณ',
    documentProcessingPriorityUpgrade: 'ประมวลผลข้อมูลได้มากขึ้นด้วยความแม่นยําที่สูงขึ้นด้วยความเร็วที่เร็วขึ้น',
    priority: {
      'standard': 'มาตรฐาน',
      'priority': 'สำคัญ',
      'top-priority': 'ลําดับความสําคัญสูงสุด',
    },
    logsHistory: 'ประวัติการบันทึก',
    customTools: 'เครื่องมือที่กําหนดเอง',
    unavailable: 'ไม่',
    days: 'วัน',
    unlimited: 'จำกัด',
    support: 'สนับสนุน',
    supportItems: {
      communityForums: 'ฟอรัมชุมชน',
      emailSupport: 'การสนับสนุนทางอีเมล',
      priorityEmail: 'การสนับสนุนทางอีเมลและแชทลําดับความสําคัญ',
      logoChange: 'การเปลี่ยนโลโก้',
      SSOAuthentication: 'การตรวจสอบสิทธิ์ SSO',
      personalizedSupport: 'การสนับสนุนส่วนบุคคล',
      dedicatedAPISupport: 'รองรับ API เฉพาะ',
      customIntegration: 'การผสานรวมและการสนับสนุนแบบกําหนดเอง',
      ragAPIRequest: 'คําขอ RAG API',
      bulkUpload: 'อัปโหลดเอกสารจํานวนมาก',
      agentMode: 'โหมดตัวแทน',
      workflow: 'เวิร์กโฟลว์',
      llmLoadingBalancing: 'โหลดบาลานซ์ LLM',
      llmLoadingBalancingTooltip: 'เพิ่มคีย์ API หลายคีย์ให้กับโมเดล โดยข้ามขีดจํากัดอัตรา API ได้อย่างมีประสิทธิภาพ',
    },
    comingSoon: 'เร็ว ๆ นี้',
    member: 'สมาชิก',
    memberAfter: 'สมาชิก',
    messageRequest: {
      title: 'เครดิตข้อความ',
      tooltip: 'โควต้าการเรียกใช้ข้อความสําหรับแผนต่างๆ โดยใช้โมเดล OpenAI (ยกเว้น gpt4) ข้อความที่เกินขีดจํากัดจะใช้คีย์ OpenAI API ของคุณ',
      titlePerMonth: '{{count,number}} ข้อความ/เดือน',
    },
    annotatedResponse: {
      title: 'ขีดจํากัดโควต้าคําอธิบายประกอบ',
      tooltip: 'การแก้ไขและคําอธิบายประกอบการตอบกลับด้วยตนเองให้ความสามารถในการตอบคําถามคุณภาพสูงที่ปรับแต่งได้สําหรับแอป (ใช้ได้เฉพาะในแอปแชท)',
    },
    ragAPIRequestTooltip: 'หมายถึงจํานวนการเรียก API ที่เรียกใช้เฉพาะความสามารถในการประมวลผลฐานความรู้ของ Dify',
    receiptInfo: 'เฉพาะเจ้าของทีมและผู้ดูแลทีมเท่านั้นที่สามารถสมัครสมาชิกและดูข้อมูลการเรียกเก็บเงินได้',
    cloud: 'บริการคลาวด์',
    comparePlanAndFeatures: 'เปรียบเทียบแผนและฟีเจอร์',
    apiRateLimit: 'ข้อจำกัดอัตราการใช้ API',
    getStarted: 'เริ่มต้น',
    documents: '{{count,number}} เอกสารความรู้',
    freeTrialTipPrefix: 'ลงทะเบียนและรับ',
    teamMember_one: '{{count,number}} สมาชิกทีม',
    unlimitedApiRate: 'ไม่มีข้อจำกัดอัตราการเรียก API',
    self: 'โฮสต์ด้วยตัวเอง',
    apiRateLimitUnit: '{{count,number}}/วัน',
    teamMember_other: '{{count,number}} สมาชิกทีม',
    teamWorkspace: '{{count,number}} ทีมทำงาน',
    priceTip: 'ต่อพื้นที่ทำงาน/',
    documentsTooltip: 'โควต้าสำหรับจำนวนเอกสารที่นำเข้าจากแหล่งข้อมูลความรู้.',
    documentsRequestQuota: '{{count,number}}/นาที จำกัด อัตราการร้องขอข้อมูล',
    apiRateLimitTooltip: 'ข้อจำกัดการใช้งาน API จะใช้กับคำขอทั้งหมดที่ทำผ่าน Dify API รวมถึงการสร้างข้อความ, การสนทนาแชท, การดำเนินการเวิร์กโฟลว์ และการประมวลผลเอกสาร.',
    freeTrialTipSuffix: 'ไม่จำเป็นต้องใช้บัตรเครดิต',
    freeTrialTip: 'ทดลองใช้งานฟรี 200 ครั้งสำหรับ OpenAI.',
    annualBilling: 'การเรียกเก็บเงินประจำปี',
    documentsRequestQuotaTooltip: 'ระบุจำนวนรวมของการกระทำที่เวิร์กสเปซสามารถดำเนินการต่อหนึ่งนาทีภายในฐานความรู้ รวมถึงการสร้างชุดข้อมูล การลบ การอัปเดต การอัปโหลดเอกสาร การปรับเปลี่ยน การเก็บถาวร และการสอบถามฐานความรู้ เมตริกนี้ถูกใช้ในการประเมินประสิทธิภาพของคำขอฐานความรู้ ตัวอย่างเช่น หากผู้ใช้ Sandbox ทำการทดสอบการตี 10 ครั้งต่อเนื่องภายในหนึ่งนาที เวิร์กสเปซของพวกเขาจะถูกจำกัดชั่วคราวในการดำเนินการต่อไปนี้ในนาทีถัดไป: การสร้างชุดข้อมูล การลบ การอัปเดต หรือการอัปโหลดหรือปรับเปลี่ยนเอกสาร.',
  },
  plans: {
    sandbox: {
      name: 'กระบะทราย',
      description: 'ทดลองใช้ GPT ฟรี 200 ครั้ง',
      includesTitle: 'มี:',
      for: 'ทดลองใช้ฟรีของความสามารถหลัก',
    },
    professional: {
      name: 'มืออาชีพ',
      description: 'สําหรับบุคคลและทีมขนาดเล็กเพื่อปลดล็อกพลังงานมากขึ้นในราคาย่อมเยา',
      includesTitle: 'ทุกอย่างในแผนฟรี รวมถึง:',
      for: 'สำหรับนักพัฒนาที่เป็นอิสระ/ทีมขนาดเล็ก',
    },
    team: {
      name: 'ทีม',
      description: 'ทํางานร่วมกันอย่างไร้ขีดจํากัดและเพลิดเพลินไปกับประสิทธิภาพระดับสูงสุด',
      includesTitle: 'ทุกอย่างในแผน Professional รวมถึง:',
      for: 'สำหรับทีมขนาดกลาง',
    },
    enterprise: {
      name: 'กิจการ',
      description: 'รับความสามารถและการสนับสนุนเต็มรูปแบบสําหรับระบบที่สําคัญต่อภารกิจขนาดใหญ่',
      includesTitle: 'ทุกอย่างในแผนทีม รวมถึง:',
      features: {
        8: 'การสนับสนุนทางเทคนิคระดับมืออาชีพ',
        2: 'คุณสมบัติพิเศษขององค์กร',
        3: 'หลายพื้นที่ทำงานและการบริหารจัดการองค์กร',
        4: 'SSO',
        6: 'ความปลอดภัยและการควบคุมขั้นสูง',
        5: 'เจรจาข้อตกลงบริการ (SLA) โดย Dify Partners',
        7: 'การอัปเดตและการบำรุงรักษาโดย Dify อย่างเป็นทางการ',
        1: 'ใบอนุญาตการใช้เชิงพาณิชย์',
        0: 'โซลูชันการปรับใช้ที่มีขนาดใหญ่และมีคุณภาพระดับองค์กร',
      },
      btnText: 'ติดต่อฝ่ายขาย',
      price: 'ที่กำหนดเอง',
      for: 'สำหรับทีมขนาดใหญ่',
      priceTip: 'การเรียกเก็บเงินประจำปีเท่านั้น',
    },
    community: {
      features: {
        2: 'ปฏิบัติตามใบอนุญาตโอเพ่นซอร์สของ Dify',
        0: 'ฟีเจอร์หลักทั้งหมดถูกปล่อยออกภายใต้ที่เก็บสาธารณะ',
        1: 'พื้นที่ทำงานเดียว',
      },
      name: 'ชุมชน',
      price: 'ฟรี',
      includesTitle: 'คุณสมบัติเสรี:',
      description: 'สำหรับผู้ใช้ส่วนบุคคล ทีมขนาดเล็ก หรือโครงการที่ไม่ใช่เชิงพาณิชย์',
      btnText: 'เริ่มต้นกับชุมชน',
      for: 'สำหรับผู้ใช้ส่วนบุคคล ทีมขนาดเล็ก หรือโครงการที่ไม่ใช่เชิงพาณิชย์',
    },
    premium: {
      features: {
        3: 'การสนับสนุนทางอีเมลและแชทที่มีความสำคัญ',
        1: 'พื้นที่ทำงานเดียว',
        2: 'การปรับแต่งโลโก้และแบรนดิ้งของเว็บแอป',
        0: 'การจัดการความน่าเชื่อถือด้วยตนเองโดยผู้ให้บริการคลาวด์ต่าง ๆ',
      },
      priceTip: 'อิงตามตลาดคลาวด์',
      for: 'สำหรับองค์กรและทีมขนาดกลาง',
      btnText: 'รับพรีเมียมใน',
      includesTitle: 'ทุกอย่างจากชุมชน รวมถึง:',
      description: 'สำหรับองค์กรและทีมขนาดกลาง',
      name: 'พรีเมียม',
      comingSoon: 'การสนับสนุน Microsoft Azure และ Google Cloud กำลังมาเร็วๆ นี้',
      price: 'ขยายได้',
    },
  },
  vectorSpace: {
    fullTip: 'เวกเตอร์สเปซเต็ม',
    fullSolution: 'อัปเกรดแผนของคุณเพื่อเพิ่มพื้นที่',
  },
  apps: {
    fullTipLine1: 'อัปเกรดแผนของคุณเป็น',
    fullTipLine2: 'สร้างแอปเพิ่มเติม',
    contactUs: 'ติดต่อเรา',
    fullTip2: 'ถึงขีดจำกัดของแผนแล้ว',
    fullTip1: 'อัปเกรดเพื่อสร้างแอปเพิ่มเติม',
    fullTip1des: 'คุณได้ถึงขีด จำกัด ของการสร้างแอปในแผนนี้แล้ว',
    fullTip2des: 'แนะนำให้ทำความสะอาดแอปพลิเคชันที่ไม่ใช้งานเพื่อเพิ่มการใช้งาน หรือติดต่อเรา',
  },
  annotatedResponse: {
    fullTipLine1: 'อัปเกรดแผนของคุณเป็น',
    fullTipLine2: 'ใส่คําอธิบายประกอบการสนทนาเพิ่มเติม',
    quotaTitle: 'โควต้าตอบกลับคําอธิบายประกอบ',
  },
  usagePage: {
    buildApps: 'สร้างแอป',
    annotationQuota: 'โควตาการประกาศ',
    documentsUploadQuota: 'โควต้าการอัปโหลดเอกสาร',
    teamMembers: 'สมาชิกในทีม',
    vectorSpace: 'การจัดเก็บข้อมูลความรู้',
    vectorSpaceTooltip: 'เอกสารที่ใช้โหมดการจัดทำดัชนีคุณภาพสูงจะใช้ทรัพยากรเก็บข้อมูลความรู้ เมื่อการเก็บข้อมูลความรู้ถึงขีดจำกัด เอกสารใหม่จะไม่สามารถอัปโหลดได้.',
  },
  teamMembers: 'สมาชิกในทีม',
}

export default translation
