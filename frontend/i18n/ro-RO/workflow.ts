const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON><PERSON>',
    redo: '<PERSON><PERSON><PERSON>',
    editing: '<PERSON><PERSON>',
    autoSaved: 'Salvat automat',
    unpublished: 'Nepublicat',
    published: 'Publicat',
    publish: 'Publică',
    update: 'Actualizează',
    run: '<PERSON>az<PERSON>',
    running: 'Rulând',
    inRunMode: 'În modul de rulare',
    inPreview: 'În previzualizare',
    inPreviewMode: 'În modul de previzualizare',
    preview: 'Previzualizează',
    viewRunHistory: 'Vezi istoricul rulărilor',
    runHistory: 'Istoric rulări',
    goBackToEdit: 'Înap<PERSON> la editor',
    conversationLog: 'Jurnal conversație',
    features: 'Funcționalități',
    debugAndPreview: 'Previzualizare',
    restart: 'Repornește',
    currentDraft: 'Schimbare curentă',
    currentDraftUnpublished: 'Schimbare curentă nepublicată',
    latestPublished: 'Ultima publicare',
    publishedAt: 'Publicat la',
    restore: 'Restaurează',
    runApp: 'Rulează aplicația',
    batchRunApp: 'Rulează aplicația în lot',
    accessAPIReference: 'Accesează referința API',
    embedIntoSite: 'Incorporează în site',
    addTitle: 'Adaugă titlu...',
    addDescription: 'Adaugă descriere...',
    noVar: 'Fără variabilă',
    searchVar: 'Caută variabilă',
    variableNamePlaceholder: 'Nume variabilă',
    setVarValuePlaceholder: 'Setează valoarea variabilei',
    needConnectTip: 'Acest pas nu este conectat la nimic',
    maxTreeDepth: 'Limită maximă de {{depth}} noduri pe ramură',
    workflowProcess: 'Proces de flux de lucru',
    notRunning: 'Încă nu rulează',
    previewPlaceholder: 'Introduceți conținutul în caseta de mai jos pentru a începe depanarea Chatbotului',
    effectVarConfirm: {
      title: 'Elimină variabila',
      content: 'Variabila este utilizată în alte noduri. Doriți să o eliminați oricum?',
    },
    insertVarTip: 'Apăsați tasta \'/\' pentru a insera rapid',
    processData: 'Procesează date',
    input: 'Intrare',
    output: 'Ieșire',
    jinjaEditorPlaceholder: 'Tastați \'/\' sau \'{\' pentru a insera variabila',
    viewOnly: 'Vizualizare doar',
    showRunHistory: 'Afișează istoricul rulărilor',
    enableJinja: 'Activează suportul pentru șabloane Jinja',
    learnMore: 'Află mai multe',
    copy: 'Copiază',
    duplicate: 'Duplică',
    pasteHere: 'Lipește aici',
    pointerMode: 'Modul pointer',
    handMode: 'Modul mână',
    model: 'Model',
    workflowAsTool: 'Flux de lucru ca instrument',
    configureRequired: 'Configurare necesară',
    configure: 'Configurează',
    manageInTools: 'Gestionează în instrumente',
    workflowAsToolTip: 'Reconfigurarea instrumentului este necesară după actualizarea fluxului de lucru.',
    viewDetailInTracingPanel: 'Vezi detalii',
    overwriteAndImport: 'Suprascriere și import',
    chooseDSL: 'Alegeți fișierul DSL(yml)',
    syncingData: 'Sincronizarea datelor, doar câteva secunde.',
    importDSL: 'Importați DSL',
    importFailure: 'Eșecul importului',
    importSuccess: 'Succesul importului',
    backupCurrentDraft: 'Backup curent draft',
    importDSLTip: 'Proiectul curent va fi suprascris. Exportați fluxul de lucru ca backup înainte de import.',
    parallelTip: {
      click: {
        title: 'Clic',
        desc: 'pentru a adăuga',
      },
      drag: {
        title: 'Glisa',
        desc: 'pentru a vă conecta',
      },
      depthLimit: 'Limita straturilor de imbricare paralelă a {{num}} straturi',
      limit: 'Paralelismul este limitat la {{num}} ramuri.',
    },
    parallelRun: 'Rulare paralelă',
    disconnect: 'Deconecta',
    jumpToNode: 'Sari la acest nod',
    addParallelNode: 'Adăugare nod paralel',
    parallel: 'PARALEL',
    branch: 'RAMURĂ',
    featuresDescription: 'Îmbunătățiți experiența utilizatorului aplicației web',
    featuresDocLink: 'Află mai multe',
    fileUploadTip: 'Funcțiile de încărcare a imaginilor au fost actualizate la încărcarea fișierelor.',
    ImageUploadLegacyTip: 'Acum puteți crea variabile de tip de fișier în formularul de pornire. Nu vom mai accepta funcția de încărcare a imaginilor în viitor.',
    importWarning: 'Prudență',
    importWarningDetails: 'Diferența de versiune DSL poate afecta anumite caracteristici',
    openInExplore: 'Deschide în Explorează',
    onFailure: 'În caz de eșec',
    addFailureBranch: 'Adăugare ramură Fail',
    noHistory: 'Fără istorie',
    loadMore: 'Încărcați mai multe fluxuri de lucru',
    exportImage: 'Exportă imaginea',
    exportSVG: 'Exportă ca SVG',
    exportPNG: 'Exportă ca PNG',
    noExist: 'Nu există o astfel de variabilă',
    exitVersions: 'Ieșire Versiuni',
    versionHistory: 'Istoricul versiunilor',
    publishUpdate: 'Publicați actualizarea',
    referenceVar: 'Variabilă de referință',
    exportJPEG: 'Exportă ca JPEG',
    addBlock: 'Adaugă nod',
    needAnswerNode: 'Nodul de răspuns trebuie adăugat',
    needEndNode: 'Nodul de sfârșit trebuie adăugat',
    tagBound: 'Numărul de aplicații care folosesc acest tag',
  },
  env: {
    envPanelTitle: 'Variabile de Mediu',
    envDescription: 'Variabilele de mediu pot fi utilizate pentru a stoca informații private și credențiale. Acestea sunt doar pentru citire și pot fi separate de fișierul DSL în timpul exportului.',
    envPanelButton: 'Adaugă Variabilă',
    modal: {
      title: 'Adaugă Variabilă de Mediu',
      editTitle: 'Editează Variabilă de Mediu',
      type: 'Tip',
      name: 'Nume',
      namePlaceholder: 'nume mediu',
      value: 'Valoare',
      valuePlaceholder: 'valoare mediu',
      secretTip: 'Utilizat pentru a defini informații sau date sensibile, cu setări DSL configurate pentru prevenirea scurgerilor.',
      description: 'Descriere',
      descriptionPlaceholder: 'Descrieți variabila',
    },
    export: {
      title: 'Exportă variabile de mediu secrete?',
      checkbox: 'Exportă valori secrete',
      ignore: 'Exportă DSL',
      export: 'Exportă DSL cu valori secrete',
    },
  },
  chatVariable: {
    panelTitle: 'Variabile de Conversație',
    panelDescription: 'Variabilele de Conversație sunt utilizate pentru a stoca informații interactive pe care LLM trebuie să le rețină, inclusiv istoricul conversației, fișiere încărcate, preferințele utilizatorului. Acestea sunt citibile și inscriptibile.',
    docLink: 'Vizitați documentația noastră pentru a afla mai multe.',
    button: 'Adăugare Variabilă',
    modal: {
      title: 'Adăugare Variabilă de Conversație',
      editTitle: 'Editare Variabilă de Conversație',
      name: 'Nume',
      namePlaceholder: 'Numele variabilei',
      type: 'Tip',
      value: 'Valoare Implicită',
      valuePlaceholder: 'Valoare implicită, lăsați gol pentru a nu seta',
      description: 'Descriere',
      descriptionPlaceholder: 'Descrieți variabila',
      editInJSON: 'Editare în JSON',
      oneByOne: 'Adăugare una câte una',
      editInForm: 'Editare în Formular',
      arrayValue: 'Valoare',
      addArrayValue: 'Adăugare Valoare',
      objectKey: 'Cheie',
      objectType: 'Tip',
      objectValue: 'Valoare Implicită',
    },
    storedContent: 'Conținut stocat',
    updatedAt: 'Actualizat la ',
  },
  changeHistory: {
    title: 'Istoric modificări',
    placeholder: 'Nu ați schimbat nimic încă',
    clearHistory: 'Șterge istoricul',
    hint: 'Sfat',
    hintText: 'Acțiunile dvs. de editare sunt urmărite într-un istoric al modificărilor, care este stocat pe dispozitivul dvs. pe durata acestei sesiuni. Acest istoric va fi șters când veți părăsi editorul.',
    stepBackward_one: '{{count}} pas înapoi',
    stepBackward_other: '{{count}} pași înapoi',
    stepForward_one: '{{count}} pas înainte',
    stepForward_other: '{{count}} pași înainte',
    sessionStart: 'Începutul sesiuni',
    currentState: 'Stare actuală',
    noteAdd: 'Notă adăugată',
    noteChange: 'Notă modificată',
    noteDelete: 'Notă ștearsă',
    nodeResize: 'Nod redimensionat',
    nodeConnect: 'Nod conectat',
    nodeTitleChange: 'Titlul nodului a fost schimbat',
    nodeChange: 'Nodul s-a schimbat',
    nodePaste: 'Node lipit',
    nodeDelete: 'Nod șters',
    nodeDescriptionChange: 'Descrierea nodului a fost modificată',
    edgeDelete: 'Nod deconectat',
    nodeAdd: 'Nod adăugat',
    nodeDragStop: 'Nod mutat',
  },
  errorMsg: {
    fieldRequired: '{{field}} este obligatoriu',
    authRequired: 'Autorizarea este necesară',
    invalidJson: '{{field}} este un JSON invalid',
    fields: {
      variable: 'Nume variabilă',
      variableValue: 'Valoare variabilă',
      code: 'Cod',
      model: 'Model',
      rerankModel: 'Model de rerankare',
      visionVariable: 'Vizibilitate variabilă',
    },
    invalidVariable: 'Variabilă invalidă',
    rerankModelRequired: 'Înainte de a activa modelul de reclasificare, vă rugăm să confirmați că modelul a fost configurat cu succes în setări.',
    toolParameterRequired: '{{field}}: parametrul [{{param}}] este obligatoriu',
    noValidTool: '{{field}} nu a fost selectat niciun instrument valid',
  },
  singleRun: {
    testRun: 'Rulare de test ',
    startRun: 'Începe rularea',
    running: 'Rulând',
    testRunIteration: 'Iterație rulare de test',
    back: 'Înapoi',
    iteration: 'Iterație',
    loop: 'Loop',
  },
  tabs: {
    'tools': 'Instrumente',
    'allTool': 'Toate',
    'builtInTool': 'Integrat',
    'customTool': 'Personalizat',
    'workflowTool': 'Flux de lucru',
    'question-understand': 'Înțelegerea întrebărilor',
    'logic': 'Logică',
    'transform': 'Transformare',
    'utilities': 'Utilități',
    'noResult': 'Niciun rezultat găsit',
    'searchTool': 'Instrument de căutare',
    'agent': 'Strategia agentului',
    'plugin': 'Plugin',
    'blocks': 'Noduri',
    'searchBlock': 'Căutare nod',
    'addAll': 'Adaugă tot',
    'allAdded': 'Toate adăugate',
  },
  blocks: {
    'start': 'Începe',
    'end': 'Sfârșit',
    'answer': 'Răspuns',
    'llm': 'LLM',
    'knowledge-retrieval': 'Recuperare de cunoștințe',
    'question-classifier': 'Clasificator de întrebări',
    'if-else': 'Dacă/Altminteri',
    'code': 'Cod',
    'template-transform': 'Șablon',
    'http-request': 'Cerere HTTP',
    'variable-assigner': 'Asignator de variabile',
    'variable-aggregator': 'Agregator de variabile',
    'assigner': 'Asignator de Variabile',
    'iteration-start': 'Început de iterație',
    'iteration': 'Iterație',
    'parameter-extractor': 'Extractor de parametri',
    'list-operator': 'Operator de listă',
    'document-extractor': 'Extractor de documente',
    'agent': 'Agent',
    'loop': 'Loop',
    'loop-end': 'Ieșire din buclă',
    'loop-start': 'Întreținere buclă',
  },
  blocksAbout: {
    'start': 'Definiți parametrii inițiali pentru lansarea unui flux de lucru',
    'end': 'Definiți sfârșitul și tipul rezultatului unui flux de lucru',
    'answer': 'Definiți conținutul răspunsului unei conversații',
    'llm': 'Invocarea modelelor de limbaj mari pentru a răspunde la întrebări sau pentru a procesa limbajul natural',
    'knowledge-retrieval': 'Permite interogarea conținutului textului legat de întrebările utilizatorului din baza de cunoștințe',
    'question-classifier': 'Definiți condițiile de clasificare a întrebărilor utilizatorului, LLM poate defini cum progresează conversația pe baza descrierii clasificării',
    'if-else': 'Permite împărțirea fluxului de lucru în două ramuri pe baza condițiilor if/else',
    'code': 'Executați un fragment de cod Python sau NodeJS pentru a implementa logică personalizată',
    'template-transform': 'Convertiți datele în șiruri de caractere folosind sintaxa șablonului Jinja',
    'http-request': 'Permite trimiterea cererilor de server prin protocolul HTTP',
    'variable-assigner': 'Agregarea variabilelor din mai multe ramuri într-o singură variabilă pentru configurarea unificată a nodurilor ulterioare.',
    'assigner': 'Nodul de atribuire a variabilelor este utilizat pentru a atribui valori variabilelor inscriptibile (precum variabilele de conversație).',
    'variable-aggregator': 'Agregarea variabilelor din mai multe ramuri într-o singură variabilă pentru configurarea unificată a nodurilor ulterioare.',
    'iteration': 'Efectuați mai mulți pași pe un obiect listă până când toate rezultatele sunt produse.',
    'parameter-extractor': 'Utilizați LLM pentru a extrage parametrii structurați din limbajul natural pentru invocările de instrumente sau cererile HTTP.',
    'list-operator': 'Folosit pentru a filtra sau sorta conținutul matricei.',
    'document-extractor': 'Folosit pentru a analiza documentele încărcate în conținut text care este ușor de înțeles de LLM.',
    'agent': 'Invocarea modelelor lingvistice mari pentru a răspunde la întrebări sau pentru a procesa limbajul natural',
    'loop': 'Executați o buclă de logică până când condiția de terminare este îndeplinită sau numărul maxim de bucle este atins.',
    'loop-end': 'Echivalent cu „break”. Acest nod nu are elemente de configurare. Când corpul buclei ajunge la acest nod, bucla se termină.',
  },
  operator: {
    zoomIn: 'Mărește',
    zoomOut: 'Micșorează',
    zoomTo50: 'Mărește la 50%',
    zoomTo100: 'Mărește la 100%',
    zoomToFit: 'Mărește pentru a se potrivi',
  },
  panel: {
    userInputField: 'Câmp de introducere utilizator',
    helpLink: 'Link de ajutor',
    about: 'Despre',
    createdBy: 'Creat de ',
    nextStep: 'Pasul următor',
    runThisStep: 'Rulează acest pas',
    checklist: 'Lista de verificare',
    checklistTip: 'Asigurați-vă că toate problemele sunt rezolvate înainte de publicare',
    checklistResolved: 'Toate problemele au fost rezolvate',
    change: 'Schimbă',
    optional: '(opțional)',
    moveToThisNode: 'Mutați la acest nod',
    organizeBlocks: 'Organizează nodurile',
    addNextStep: 'Adăugați următorul pas în acest flux de lucru',
    changeBlock: 'Schimbă nodul',
    selectNextStep: 'Selectați Pasul Următor',
    maximize: 'Maximize Canvas',
    minimize: 'Iesi din modul pe tot ecranul',
  },
  nodes: {
    common: {
      outputVars: 'Variabile de ieșire',
      insertVarTip: 'Inserează variabilă',
      memory: {
        memory: 'Memorie',
        memoryTip: 'Setări de memorie pentru conversație',
        windowSize: 'Dimensiunea ferestrei',
        conversationRoleName: 'Numele rolului în conversație',
        user: 'Prefix utilizator',
        assistant: 'Prefix asistent',
      },
      memories: {
        title: 'Amintiri',
        tip: 'Memoria conversației',
        builtIn: 'Integrat',
      },
      errorHandle: {
        none: {
          title: 'Niciunul',
          desc: 'Nodul se va opri din rulare dacă apare o excepție și nu este gestionat',
        },
        defaultValue: {
          title: 'Valoare implicită',
          desc: 'Când apare o eroare, specificați un conținut de ieșire static.',
          tip: 'În caz de eroare, va reveni sub valoare.',
          inLog: 'Excepție de nod, ieșire în funcție de valorile implicite.',
          output: 'Valoare implicită de ieșire',
        },
        failBranch: {
          title: 'Ramură Fail',
          desc: 'Când apare o eroare, va executa ramura de excepție',
          customize: 'Accesați pânza pentru a personaliza logica ramurii de eșec.',
          inLog: 'Excepția nodului, va executa automat ramura de eșec. Ieșirea nodului va returna un tip de eroare și un mesaj de eroare și le va transmite în aval.',
          customizeTip: 'Când ramura de eșec este activată, excepțiile aruncate de noduri nu vor încheia procesul. În schimb, va executa automat ramura de eșec predefinită, permițându-vă să furnizați în mod flexibil mesaje de eroare, rapoarte, remedieri sau acțiuni de omitere.',
        },
        partialSucceeded: {
          tip: 'Există {{num}} noduri în proces care rulează anormal, vă rugăm să mergeți la urmărire pentru a verifica jurnalele.',
        },
        title: 'Gestionarea erorilor',
        tip: 'Strategie de gestionare a excepțiilor, declanșată atunci când un nod întâlnește o excepție.',
      },
      retry: {
        retry: 'Reîncercare',
        retryOnFailure: 'Reîncercați în caz de eșec',
        maxRetries: 'numărul maxim de încercări',
        retryInterval: 'Interval de reîncercare',
        retrying: 'Reîncerca...',
        retrySuccessful: 'Reîncercați cu succes',
        retryFailed: 'Reîncercarea a eșuat',
        retryFailedTimes: '{{times}} reîncercări eșuate',
        times: 'Ori',
        ms: 'Ms',
        retries: '{{num}} Încercări',
        retryTimes: 'Reîncercați {{times}} ori în caz de eșec',
      },
      typeSwitch: {
        variable: 'Folosește variabila',
        input: 'Valoare de intrare',
      },
    },
    start: {
      required: 'necesar',
      inputField: 'Câmp de intrare',
      builtInVar: 'Variabile integrate',
      outputVars: {
        query: 'Intrare utilizator',
        memories: {
          des: 'Istoric conversație',
          type: 'tip mesaj',
          content: 'conținut mesaj',
        },
        files: 'Listă de fișiere',
      },
      noVarTip: 'Setați intrările care pot fi utilizate în fluxul de lucru',
    },
    end: {
      outputs: 'Ieșiri',
      output: {
        type: 'tip ieșire',
        variable: 'variabilă de ieșire',
      },
      type: {
        'none': 'Nimic',
        'plain-text': 'Text simplu',
        'structured': 'Structurat',
      },
    },
    answer: {
      answer: 'Răspuns',
      outputVars: 'Variabile de ieșire',
    },
    llm: {
      model: 'model',
      variables: 'variabile',
      context: 'context',
      contextTooltip: 'Puteți importa cunoștințe ca și context',
      notSetContextInPromptTip: 'Pentru a activa funcția de context, completați variabila de context în PROMPT.',
      prompt: 'prompt',
      roleDescription: {
        system: 'Dați instrucțiuni de nivel înalt pentru conversație',
        user: 'Furnizați instrucțiuni, întrebări sau orice intrare bazată pe text pentru model',
        assistant: 'Răspunsurile modelului bazate pe mesajele utilizatorului',
      },
      addMessage: 'Adaugă mesaj',
      vision: 'viziune',
      files: 'Fișiere',
      resolution: {
        name: 'Rezoluție',
        high: 'Înaltă',
        low: 'Joasă',
      },
      outputVars: {
        output: 'Conținut generat',
        usage: 'Informații de utilizare a modelului',
      },
      singleRun: {
        variable: 'Variabilă',
      },
      sysQueryInUser: 'sys.query în mesajul utilizatorului este necesar',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Vă rugăm să terminați editarea câmpului curent înainte de a salva schema.',
        },
        addChildField: 'Adăugați câmpul copil',
        generationTip: 'Puteți folosi limbajul natural pentru a crea rapid un schema JSON.',
        promptTooltip: 'Convertește descrierea textului într-o structură standardizată JSON Schema.',
        resetDefaults: 'Resetează',
        apply: 'Aplică',
        instruction: 'Instrucțiune',
        doc: 'Aflați mai multe despre ieșirea structurată',
        stringValidations: 'Validările șirurilor',
        title: 'Schema de Ieşire Structurată',
        generateJsonSchema: 'Generați schema JSON',
        generate: 'Generează',
        import: 'Importă din JSON',
        generating: 'Generarea schemei JSON...',
        addField: 'Adaugă câmp',
        regenerate: 'Regenerare',
        generatedResult: 'Rezultatul generat',
        descriptionPlaceholder: 'Adăugați o descriere',
        showAdvancedOptions: 'Afișați opțiuni avansate',
        resultTip: 'Iată rezultatul generat. Dacă nu ești mulțumit, poți să te întorci și să îți modifici cererea.',
        fieldNamePlaceholder: 'Numele câmpului',
        required: 'Necesar',
        back: 'Înapoi',
        promptPlaceholder: 'Descrie schema ta JSON...',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Variabilă de interogare',
      knowledge: 'Cunoștințe',
      outputVars: {
        output: 'Date segmentate recuperate',
        content: 'Conținut segmentat',
        title: 'Titlu segmentat',
        icon: 'Pictogramă segmentată',
        url: 'URL segmentat',
        metadata: 'Alte metadate',
      },
      metadata: {
        options: {
          disabled: {
            subTitle: 'Nu activarea filtrării metadatelor',
            title: 'Dezactivat',
          },
          automatic: {
            subTitle: 'Generați automat condiții de filtrare a metadatelor pe baza interogării utilizatorului',
            desc: 'Generați automat condiții de filtrare a metadatelor pe baza variabilei de interogare',
            title: 'Automat',
          },
          manual: {
            subTitle: 'Adăugați manual condiții de filtrare a metadatelor',
            title: 'Manual',
          },
        },
        panel: {
          conditions: 'Condiții',
          select: 'Selectați variabila...',
          title: 'Condiții de filtrare a metadatelor',
          add: 'Adaugă condiție',
          placeholder: 'Introduceți valoarea',
          datePlaceholder: 'Alege o oră...',
          search: 'Căutare metadate',
        },
        title: 'Filtrarea metadatelor',
      },
    },
    http: {
      inputVars: 'Variabile de intrare',
      api: 'API',
      apiPlaceholder: 'Introduceți URL-ul, tastați ‘/’ pentru a insera variabilă',
      notStartWithHttp: 'API-ul trebuie să înceapă cu http:// sau https://',
      key: 'Cheie',
      value: 'Valoare',
      bulkEdit: 'Editare în masă',
      keyValueEdit: 'Editare cheie-valoare',
      headers: 'Antete',
      params: 'Parametri',
      body: 'Corp',
      outputVars: {
        body: 'Conținutul răspunsului',
        statusCode: 'Cod de stare al răspunsului',
        headers: 'Lista antetelor de răspuns în format JSON',
        files: 'Lista fișierelor',
      },
      authorization: {
        'authorization': 'Autorizare',
        'authorizationType': 'Tip de autorizare',
        'no-auth': 'Niciuna',
        'api-key': 'Cheie API',
        'auth-type': 'Tip de autentificare',
        'basic': 'De bază',
        'bearer': 'Bearer',
        'custom': 'Personalizat',
        'api-key-title': 'Cheie API',
        'header': 'Antet',
      },
      insertVarPlaceholder: 'tastați \'/\' pentru a insera variabilă',
      timeout: {
        title: 'Timp limită',
        connectLabel: 'Timp limită pentru conexiune',
        connectPlaceholder: 'Introduceți timpul limită pentru conexiune în secunde',
        readLabel: 'Timp limită pentru citire',
        readPlaceholder: 'Introduceți timpul limită pentru citire în secunde',
        writeLabel: 'Timp limită pentru scriere',
        writePlaceholder: 'Introduceți timpul limită pentru scriere în secunde',
      },
      type: 'Tip',
      binaryFileVariable: 'Variabilă de fișier binar',
      extractListPlaceholder: 'Introduceți indexul elementelor din listă, tastați "/" inserați variabila',
      curl: {
        placeholder: 'Lipiți șirul cURL aici',
        title: 'Importați din cURL',
      },
      verifySSL: {
        title: 'Verifică certificatul SSL',
        warningTooltip: 'Dezactivarea verificării SSL nu este recomandată pentru medii de producție. Acest lucru ar trebui să fie folosit doar în dezvoltare sau testare, deoarece face conexiunea vulnerabilă la amenințări de securitate, cum ar fi atacurile man-in-the-middle.',
      },
    },
    code: {
      inputVars: 'Variabile de intrare',
      outputVars: 'Variabile de ieșire',
      advancedDependencies: 'Dependențe avansate',
      advancedDependenciesTip: 'Adăugați câteva dependențe preîncărcate care necesită mai mult timp pentru a consuma sau nu sunt integrate implicit aici',
      searchDependencies: 'Căutați dependențe',
      syncFunctionSignature: 'Sincronizați semnătura funcției cu codul',
    },
    templateTransform: {
      inputVars: 'Variabile de intrare',
      code: 'Cod',
      codeSupportTip: 'Suportă doar Jinja2',
      outputVars: {
        output: 'Conținut transformat',
      },
    },
    ifElse: {
      if: 'Dacă',
      else: 'Altminteri',
      elseDescription: 'Utilizat pentru a defini logica care ar trebui executată atunci când condiția if nu este îndeplinită.',
      and: 'și',
      or: 'sau',
      operator: 'Operator',
      notSetVariable: 'Vă rugăm să setați mai întâi variabila',
      comparisonOperator: {
        'contains': 'conține',
        'not contains': 'nu conține',
        'start with': 'începe cu',
        'end with': 'se termină cu',
        'is': 'este',
        'is not': 'nu este',
        'empty': 'este gol',
        'not empty': 'nu este gol',
        'null': 'este null',
        'not null': 'nu este null',
        'regex match': 'potrivire regex',
        'in': 'în',
        'not in': 'nu în',
        'exists': 'Există',
        'all of': 'Toate',
        'not exists': 'nu există',
        'before': 'înainte',
        'after': 'după',
      },
      enterValue: 'Introduceți valoarea',
      addCondition: 'Adăugați condiție',
      conditionNotSetup: 'Condiția NU este setată',
      selectVariable: 'Selectați variabila...',
      optionName: {
        audio: 'Audio',
        localUpload: 'Încărcare locală',
        url: 'Adresa URL',
        image: 'Imagine',
        video: 'Video',
        doc: 'Doc',
      },
      select: 'Alege',
      addSubVariable: 'Subvariabilă',
      condition: 'Condiție',
    },
    variableAssigner: {
      title: 'Atribuie variabile',
      outputType: 'Tip de ieșire',
      varNotSet: 'Variabila nu este setată',
      noVarTip: 'Adăugați variabilele de atribuit',
      type: {
        string: 'Șir',
        number: 'Număr',
        object: 'Obiect',
        array: 'Array',
      },
      aggregationGroup: 'Grup de agregare',
      aggregationGroupTip: 'Activarea acestei funcții permite agregatorului de variabile să agrege mai multe seturi de variabile.',
      addGroup: 'Adăugați grup',
      outputVars: {
        varDescribe: 'Ieșire {{groupName}}',
      },
      setAssignVariable: 'Setați variabila de atribuire',
    },
    assigner: {
      'assignedVariable': 'Variabilă Atribuită',
      'writeMode': 'Mod de Scriere',
      'writeModeTip': 'Când VARIABILA ATRIBUITĂ este un array, modul de adăugare adaugă la sfârșit.',
      'over-write': 'Suprascrie',
      'append': 'Adaugă',
      'plus': 'Plus',
      'clear': 'Șterge',
      'setVariable': 'Setează Variabila',
      'variable': 'Variabilă',
      'operations': {
        'append': 'Adăugaţi',
        'extend': 'Prelungi',
        'title': 'Operație',
        '+=': '+=',
        'set': 'Apus',
        '*=': '*=',
        'overwrite': 'Suprascrie',
        'clear': 'Clar',
        'over-write': 'Suprascrie',
        '/=': '/=',
        '-=': '-=',
        'remove-first': 'Îndepărtează primul',
        'remove-last': 'Îndepărtează ultimul',
      },
      'selectAssignedVariable': 'Selectați variabila atribuită...',
      'varNotSet': 'Variabila NU este setată',
      'noVarTip': 'Faceți clic pe butonul "+" pentru a adăuga variabile',
      'noAssignedVars': 'Nu există variabile atribuite disponibile',
      'setParameter': 'Setați parametrul...',
      'assignedVarsDescription': 'Variabilele atribuite trebuie să fie variabile inscripționabile, cum ar fi variabilele de conversație.',
      'variables': 'Variabile',
    },
    tool: {
      inputVars: 'Variabile de intrare',
      outputVars: {
        text: 'conținut generat de instrument',
        files: {
          title: 'fișiere generate de instrument',
          type: 'Tip de suport. Acum acceptă doar imagine',
          transfer_method: 'Metodă de transfer. Valoarea este remote_url sau local_file',
          url: 'URL imagine',
          upload_file_id: 'ID fișier încărcat',
        },
        json: 'JSON generat de instrument',
      },
      authorize: 'Autorizați',
      insertPlaceholder2: 'introduce o variabilă',
      insertPlaceholder1: 'Scrieți sau apăsați',
      settings: 'Setări',
    },
    questionClassifiers: {
      model: 'model',
      inputVars: 'Variabile de intrare',
      outputVars: {
        className: 'Nume clasă',
        usage: 'Informații de utilizare a modelului',
      },
      class: 'Clasă',
      classNamePlaceholder: 'Scrieți numele clasei',
      advancedSetting: 'Setare avansată',
      topicName: 'Nume subiect',
      topicPlaceholder: 'Scrieți numele subiectului',
      addClass: 'Adăugați clasă',
      instruction: 'Instrucțiune',
      instructionTip: 'Introduceți instrucțiuni suplimentare pentru a ajuta clasificatorul de întrebări să înțeleagă mai bine cum să categorizeze întrebările.',
      instructionPlaceholder: 'Scrieți instrucțiunea',
    },
    parameterExtractor: {
      inputVar: 'Variabilă de intrare',
      outputVars: {
        isSuccess: 'Este succes. În caz de succes valoarea este 1, în caz de eșec valoarea este 0.',
        errorReason: 'Motivul erorii',
        usage: 'Informații de utilizare a modelului',
      },
      extractParameters: 'Extrageți parametrii',
      importFromTool: 'Importă din instrumente',
      addExtractParameter: 'Adăugați parametru de extragere',
      addExtractParameterContent: {
        name: 'Nume',
        namePlaceholder: 'Nume parametru de extragere',
        type: 'Tip',
        typePlaceholder: 'Tip parametru de extragere',
        description: 'Descriere',
        descriptionPlaceholder: 'Descriere parametru de extragere',
        required: 'Necesar',
        requiredContent: 'Necesar este utilizat doar ca referință pentru inferența modelului și nu pentru validarea obligatorie a ieșirii parametrului.',
      },
      extractParametersNotSet: 'Parametrii de extragere nu sunt setați',
      instruction: 'Instrucțiune',
      instructionTip: 'Introduceți instrucțiuni suplimentare pentru a ajuta extractorul de parametri să înțeleagă cum să extragă parametrii.',
      advancedSetting: 'Setare avansată',
      reasoningMode: 'Mod de raționament',
      reasoningModeTip: 'Puteți alege modul de raționament potrivit în funcție de capacitatea modelului de a răspunde la instrucțiuni pentru apelarea funcțiilor sau prompturi.',
    },
    iteration: {
      deleteTitle: 'Ștergeți nodul de iterație?',
      deleteDesc: 'Ștergerea nodului de iterație va șterge toate nodurile copil',
      input: 'Intrare',
      output: 'Variabile de ieșire',
      iteration_one: '{{count}} Iterație',
      iteration_other: '{{count}} Iterații',
      currentIteration: 'Iterație curentă',
      ErrorMethod: {
        operationTerminated: 'Încheiată',
        continueOnError: 'continuare-la-eroare',
        removeAbnormalOutput: 'elimină-ieșire-anormală',
      },
      parallelModeEnableTitle: 'Modul paralel activat',
      errorResponseMethod: 'Metoda de răspuns la eroare',
      comma: ',',
      parallelModeEnableDesc: 'În modul paralel, sarcinile din iterații acceptă execuția paralelă. Puteți configura acest lucru în panoul de proprietăți din dreapta.',
      parallelModeUpper: 'MOD PARALEL',
      MaxParallelismTitle: 'Paralelism maxim',
      parallelMode: 'Mod paralel',
      error_other: '{{număr}} Erori',
      error_one: '{{număr}} Eroare',
      parallelPanelDesc: 'În modul paralel, activitățile din iterație acceptă execuția paralelă.',
      MaxParallelismDesc: 'Paralelismul maxim este utilizat pentru a controla numărul de sarcini executate simultan într-o singură iterație.',
      answerNodeWarningDesc: 'Avertisment modul paralel: Nodurile de răspuns, atribuirea variabilelor de conversație și operațiunile persistente de citire/scriere în iterații pot cauza excepții.',
    },
    note: {
      editor: {
        small: 'Mic',
        bold: 'Îndrăzneț',
        unlink: 'Deconecta',
        strikethrough: 'Tăiere',
        invalidUrl: 'URL nevalidă',
        medium: 'Medie',
        openLink: 'Deschide',
        large: 'Mare',
        enterUrl: 'Introduceți adresa URL...',
        italic: 'Cursiv',
        placeholder: 'Scrie-ți notița...',
        link: 'Legătură',
        bulletList: 'Lista de marcatori',
        showAuthor: 'Afișați autorul',
      },
      addNote: 'Adăugați o notă',
    },
    docExtractor: {
      outputVars: {
        text: 'Text extras',
      },
      inputVar: 'Variabilă de intrare',
      learnMore: 'Află mai multe',
      supportFileTypes: 'Tipuri de fișiere de suport: {{types}}.',
    },
    listFilter: {
      outputVars: {
        first_record: 'Primul record',
        last_record: 'Ultima înregistrare',
        result: 'Filtrează rezultatul',
      },
      desc: 'DESC',
      inputVar: 'Variabilă de intrare',
      filterConditionKey: 'Tasta de condiție a filtrului',
      filterCondition: 'Starea filtrului',
      orderBy: 'Comandă după',
      selectVariableKeyPlaceholder: 'Selectați tasta subvariabilă',
      filterConditionComparisonOperator: 'Operator de comparare a condițiilor filtrului',
      limit: 'N de sus',
      filterConditionComparisonValue: 'Valoare Stare filtrare',
      asc: 'ASC',
      extractsCondition: 'Extrageți elementul N',
    },
    agent: {
      strategy: {
        configureTip: 'Vă rugăm să configurați strategia agentică.',
        selectTip: 'Selectați strategia agentică',
        configureTipDesc: 'După configurarea strategiei agentice, acest nod va încărca automat configurațiile rămase. Strategia va afecta mecanismul raționamentului instrumentelor în mai mulți pași.',
        shortLabel: 'Strategie',
        label: 'Strategia agentică',
        tooltip: 'Diferitele strategii agentice determină modul în care sistemul planifică și execută apelurile de instrumente în mai mulți pași',
        searchPlaceholder: 'Strategie agentică de căutare',
      },
      pluginInstaller: {
        installing: 'Instalarea',
        install: 'Instala',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'Gestionați în pluginuri',
        title: 'Model neinstalat',
        desc: 'Acest model este instalat din depozitul local sau GitHub. Vă rugăm să utilizați după instalare.',
      },
      modelNotSupport: {
        descForVersionSwitch: 'Versiunea de plugin instalată nu oferă acest model. Faceți clic pentru a comuta versiunea.',
        desc: 'Versiunea de plugin instalată nu oferă acest model.',
        title: 'Model neacceptat',
      },
      modelSelectorTooltips: {
        deprecated: 'Acest model este învechit',
      },
      outputVars: {
        files: {
          upload_file_id: 'Încărcați ID-ul fișierului',
          type: 'Tip de suport. Acum acceptă doar imaginea',
          transfer_method: 'Metoda de transfer. Valoarea este remote_url sau local_file',
          title: 'Fișiere generate de agent',
          url: 'Adresa URL a imaginii',
        },
        text: 'Conținut generat de agent',
        json: 'JSON generat de agent',
      },
      checkList: {
        strategyNotSelected: 'Strategia neselectată',
      },
      installPlugin: {
        install: 'Instala',
        changelog: 'Jurnal de modificări',
        desc: 'Despre instalarea următorului plugin',
        title: 'Instalează pluginul',
        cancel: 'Anula',
      },
      pluginNotInstalled: 'Acest plugin nu este instalat',
      unsupportedStrategy: 'Strategie neacceptată',
      notAuthorized: 'Neautorizat',
      learnMore: 'Află mai multe',
      toolbox: 'cutie de scule',
      toolNotAuthorizedTooltip: '{{instrument}} Neautorizat',
      strategyNotSet: 'Strategia agentică nu este setată',
      tools: 'Instrumente',
      maxIterations: 'Iterații maxime',
      configureModel: 'Configurați modelul',
      strategyNotFoundDescAndSwitchVersion: 'Versiunea de plugin instalată nu oferă această strategie. Faceți clic pentru a comuta versiunea.',
      strategyNotInstallTooltip: '{{strategy}} nu este instalat',
      pluginNotFoundDesc: 'Acest plugin este instalat de pe GitHub. Vă rugăm să accesați Pluginuri pentru a reinstala',
      modelNotSelected: 'Model neselectat',
      toolNotInstallTooltip: '{{tool}} nu este instalat',
      pluginNotInstalledDesc: 'Acest plugin este instalat de pe GitHub. Vă rugăm să accesați Pluginuri pentru a reinstala',
      strategyNotFoundDesc: 'Versiunea de plugin instalată nu oferă această strategie.',
      modelNotInstallTooltip: 'Acest model nu este instalat',
      linkToPlugin: 'Link către pluginuri',
      model: 'model',
      parameterSchema: 'Schema parametrului',
      clickToViewParameterSchema: 'Click pentru a vizualiza schema parametrilor',
    },
    loop: {
      ErrorMethod: {
        removeAbnormalOutput: 'Elimină ieșirea anormală',
        continueOnError: 'Continuați în caz de eroare',
        operationTerminated: 'Încetat',
      },
      inputMode: 'Mod de introducere',
      currentLoopCount: 'Numărul curent de iterații: {{count}}',
      error_one: '{{count}} Eroare',
      error_other: '{{count}} Erori',
      input: 'Intrare',
      errorResponseMethod: 'Metoda de Răspuns la Erori',
      deleteTitle: 'Șterge nodul de ciclu?',
      breakConditionTip: 'Numai variabilele din interiorul buclelor cu condiții de terminare și variabilele de conversație pot fi referite.',
      loop_one: '{{count}} buclă',
      loop_other: '{{count}} Buclă',
      loopNode: 'Nod de buclă',
      loopMaxCount: 'Numărul maxim de iterații',
      loopVariables: 'Variabile de buclă',
      finalLoopVariables: 'Variabilele ciclului final',
      currentLoop: 'Circuit Curent',
      totalLoopCount: 'Numărul total de bucle: {{count}}',
      output: 'Variabilă de ieșire',
      exitConditionTip: 'Un nod de buclă are nevoie de cel puțin o condiție de ieșire.',
      initialLoopVariables: 'Variabilele de loop inițiale',
      setLoopVariables: 'Setați variabilele în cadrul buclei',
      loopMaxCountError: 'Vă rugăm să introduceți un număr maxim valid de bucle, care să fie între 1 și {{maxCount}}',
      deleteDesc: 'Ștergerea nodului buclă va elimina toate nodurile copil.',
      breakCondition: 'Condiția de terminare a buclei',
      comma: ',',
      variableName: 'Nume Variabil',
    },
  },
  tracing: {
    stopBy: 'Oprit de {{user}}',
  },
  variableReference: {
    noAvailableVars: 'Nu există variabile disponibile',
    noVarsForOperation: 'Nu există variabile disponibile pentru atribuire cu operațiunea selectată.',
    conversationVars: 'Variabile de conversație',
    assignedVarsDescription: 'Variabilele atribuite trebuie să fie variabile inscripționabile, cum ar fi',
    noAssignedVars: 'Nu există variabile atribuite disponibile',
  },
  versionHistory: {
    filter: {
      all: 'Toate',
      onlyYours: 'Numai al tău',
      reset: 'Resetare filtrare',
      onlyShowNamedVersions: 'Afișați doar versiunile numite',
      empty: 'Nu s-a găsit nicio istorie de versiune corespunzătoare.',
    },
    editField: {
      releaseNotesLengthLimit: 'Notele de eliberare nu pot depăși {{limit}} caractere',
      title: 'Titlu',
      titleLengthLimit: 'Titlul nu poate depăși {{limit}} caractere',
      releaseNotes: 'Note de lansare',
    },
    action: {
      restoreSuccess: 'Versiune restaurată',
      deleteSuccess: 'Versiune ștearsă',
      restoreFailure: 'Restaurarea versiunii a eșuat',
      deleteFailure: 'Ștergerea versiunii a eșuat',
      updateSuccess: 'Versiune actualizată',
      updateFailure: 'Actualizarea versiunii a eșuat',
    },
    latest: 'Cea mai recentă',
    title: 'Versiuni',
    nameThisVersion: 'Numește această versiune',
    restorationTip: 'După restaurarea versiunii, proiectul actual va fi suprascris.',
    defaultName: 'Versiune fără titlu',
    editVersionInfo: 'Editează informațiile versiunii',
    releaseNotesPlaceholder: 'Descrie ce s-a schimbat',
    deletionTip: 'Ștergerea este irreversibilă, vă rugăm să confirmați.',
    currentDraft: 'Draftul curent',
  },
  debug: {
    noData: {
      runThisNode: 'Rulează acest nod',
      description: 'Rezultatele ultimei rulări vor fi afișate aici',
    },
    variableInspect: {
      trigger: {
        clear: 'Clar',
        running: 'Starea de funcționare a cache-ului',
        cached: 'Vizualizează variabilele cached',
        normal: 'Inspectare variabilă',
        stop: 'Oprește-te din alergat',
      },
      chatNode: 'Conversație',
      title: 'Inspectare variabilă',
      systemNode: 'Sistem',
      clearAll: 'Resetare toate',
      emptyLink: 'Învățați mai multe',
      view: 'Vizualizați jurnalul',
      envNode: 'Mediu',
      reset: 'Resetează la ultima valoare rulată',
      resetConversationVar: 'Resetați variabila de conversație la valoarea implicită',
      edited: 'Editat',
      clearNode: 'Șterge variabila cached',
      emptyTip: 'După ce ai trecut printr-un nod pe canvas sau ai rulat un nod pas cu pas, poți vizualiza valoarea curentă a variabilei nodului în Inspectarea Variabilelor.',
    },
    settingsTab: 'Setări',
    lastRunTab: 'Ultima execuție',
  },
}

export default translation
