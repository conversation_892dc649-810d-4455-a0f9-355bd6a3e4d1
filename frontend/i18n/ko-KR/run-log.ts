const translation = {
  input: '입력',
  result: '결과',
  detail: '상세정보',
  tracing: '트레이싱',
  resultPanel: {
    status: '상태',
    time: '소요 시간',
    tokens: '토큰 총합',
  },
  meta: {
    title: '메타데이터',
    status: '상태',
    version: '버전',
    executor: '실행자',
    startTime: '시작 시간',
    time: '소요 시간',
    tokens: '토큰 총합',
    steps: '실행 단계',
  },
  resultEmpty: {
    title: '이 실행에서는 JSON 형식만 출력됩니다',
    tipLeft: '를 방문해주세요',
    link: '상세 정보 패널',
    tipRight: '를 확인하세요.',
  },
  actionLogs: '작업 로그',
  circularInvocationTip: '현재 워크플로우에 도구/노드의 순환 호출이 있습니다.',
}

export default translation
