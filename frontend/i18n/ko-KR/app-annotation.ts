const translation = {
  title: '어노테이션',
  name: '어노테이션 답변',
  editBy: '{{author}} 님이 편집한 답변',
  noData: {
    title: '어노테이션이 없습니다',
    description: '여기에서는 앱 디버깅 중에 어노테이션을 편집하거나 일괄적으로 어노테이션을 가져와 고품질의 응답을 생성할 수 있습니다.',
  },
  table: {
    header: {
      question: '질문',
      answer: '답변',
      createdAt: '생성 날짜',
      hits: '조회수',
      actions: '액션',
      addAnnotation: '어노테이션 추가',
      bulkImport: '일괄 가져오기',
      bulkExport: '일괄 내보내기',
      clearAll: '모든 어노테이션 지우기',
    },
  },
  editModal: {
    title: '어노테이션 답변 편집',
    queryName: '사용자 쿼리',
    answerName: '스토리텔러 봇',
    yourAnswer: '당신의 답변',
    answerPlaceholder: '여기에 답변을 입력하세요',
    yourQuery: '당신의 쿼리',
    queryPlaceholder: '여기에 쿼리를 입력하세요',
    removeThisCache: '이 어노테이션 삭제',
    createdAt: '생성 날짜',
  },
  addModal: {
    title: '어노테이션 답변 추가',
    queryName: '질문',
    answerName: '답변',
    answerPlaceholder: '여기에 답변을 입력하세요',
    queryPlaceholder: '여기에 질문을 입력하세요',
    createNext: '다른 어노테이션이 달린 응답 추가',
  },
  batchModal: {
    title: '일괄 가져오기',
    csvUploadTitle: 'CSV 파일을 여기에 드래그 앤 드롭하거나,',
    browse: '찾아보기',
    tip: 'CSV 파일은 다음 구조를 따라야 합니다:',
    question: '질문',
    answer: '답변',
    contentTitle: '덩어리 내용',
    content: '내용',
    template: '여기서 템플릿 다운로드',
    cancel: '취소',
    run: '일괄 실행',
    runError: '일괄 실행 실패',
    processing: '일괄 처리 중',
    completed: '가져오기 완료',
    error: '가져오기 오류',
    ok: '확인',
  },
  errorMessage: {
    answerRequired: '답변은 필수입니다',
    queryRequired: '질문은 필수입니다',
  },
  viewModal: {
    annotatedResponse: '어노테이션 답변',
    hitHistory: '조회 기록',
    hit: '조회',
    hits: '조회수',
    noHitHistory: '조회 기록이 없습니다',
  },
  hitHistoryTable: {
    query: '쿼리',
    match: '일치',
    response: '응답',
    source: '소스',
    score: '점수',
    time: '시간',
  },
  initSetup: {
    title: '어노테이션 답변 초기 설정',
    configTitle: '어노테이션 답변 설정',
    confirmBtn: '저장하고 활성화하기',
    configConfirmBtn: '저장',
  },
  embeddingModelSwitchTip: '어노테이션 텍스트의 임베딩 모델입니다. 모델을 변경하면 다시 임베딩되며 추가 비용이 발생합니다.',
}

export default translation
