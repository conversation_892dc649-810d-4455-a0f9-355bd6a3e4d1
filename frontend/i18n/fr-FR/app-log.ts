const translation = {
  title: 'Journaux',
  description: 'Les journaux enregistrent l\'état d\'exécution de l\'application, y compris les entrées utilisateur et les réponses de l\'IA.',
  dateTimeFormat: 'MM/DD/YYYY hh:mm A',
  table: {
    header: {
      updatedTime: 'Heure de mise à jour',
      time: 'Heure de création',
      endUser: 'Utilisateur final ou compte',
      input: 'Entrée',
      output: 'Sortie',
      summary: 'Titre',
      messageCount: 'Nombre de messages',
      userRate: 'Taux utilisateur',
      adminRate: 'Taux op.',
      startTime: 'HEURE DE DÉBUT',
      status: 'STATUT',
      runtime: 'TEMPS D\'EXÉCUTION',
      tokens: 'JETONS',
      user: 'UTILISATEUR FINAL OU COMPTE',
      version: 'VERSION',
    },
    pagination: {
      previous: 'Précédent',
      next: 'Suivant',
    },
    empty: {
      noChat: 'Aucune conversation pour le moment',
      noOutput: 'Aucune sortie',
      element: {
        title: 'Y a-t-il quelqu\'un ?',
        content: 'Observez et annotez ici les interactions entre les utilisateurs finaux et les applications d\'IA pour améliorer en continu la précision de l\'IA. Vous pouvez essayer de <shareLink>partager</shareLink> ou de <testLink>tester</testLink> l\'application Web vous-même, puis revenir sur cette page.',
      },
    },
  },
  detail: {
    time: 'Heure',
    conversationId: 'ID de conversation',
    promptTemplate: 'Modèle de consigne',
    promptTemplateBeforeChat: 'Modèle de consigne avant la conversation · En tant que message système',
    annotationTip: 'Améliorations marquées par {{user}}',
    timeConsuming: '',
    second: 's',
    tokenCost: 'Jeton dépensé',
    loading: 'chargement',
    operation: {
      like: 'j\'aime',
      dislike: 'je n\'aime pas',
      addAnnotation: 'Ajouter une amélioration',
      editAnnotation: 'Modifier une amélioration',
      annotationPlaceholder: 'Entrez la réponse attendue que vous souhaitez que l\'IA donne, cela peut être utilisé pour le réglage fin du modèle et l\'amélioration continue de la qualité de génération de texte à l\'avenir.',
    },
    variables: 'Variables',
    uploadImages: 'Images téléchargées',
    modelParams: 'Paramètres du modèle',
  },
  filter: {
    period: {
      today: 'Aujourd\'hui',
      last7days: '7 derniers jours',
      last4weeks: '4 dernières semaines',
      last3months: '3 derniers mois',
      last12months: '12 derniers mois',
      monthToDate: 'Mois à ce jour',
      quarterToDate: 'Trimestre à ce jour',
      yearToDate: 'Année à ce jour',
      allTime: 'Tout le temps',
    },
    annotation: {
      all: 'Tous',
      annotated: 'Améliorations annotées ({{count}} éléments)',
      not_annotated: 'Non annoté',
    },
    sortBy: 'Trier par :',
    descending: 'décroissant',
    ascending: 'croissant',
  },
  workflowTitle: 'Journaux de workflow',
  workflowSubtitle: 'Le journal enregistre l\'opération d\'Automate.',
  runDetail: {
    title: 'Journal de conversation',
    workflowTitle: 'Détail du journal',
    fileListDetail: 'Détail',
    fileListLabel: 'Détails du fichier',
  },
  promptLog: 'Journal de consigne',
  agentLog: 'Journal des agents',
  viewLog: 'Voir le journal',
  agentLogDetail: {
    agentMode: 'Mode Agent',
    toolUsed: 'Outil utilisé',
    iterations: 'Itérations',
    iteration: 'Itération',
    finalProcessing: 'Traitement final',
  },
}

export default translation
