const translation = {
  category: {
    extensions: '拡張機能',
    all: 'すべて',
    tools: 'ツール',
    bundles: 'バンドル',
    agents: 'エージェント戦略',
    models: 'モデル',
  },
  categorySingle: {
    agent: 'エージェント戦略',
    model: 'モデル',
    bundle: 'バンドル',
    tool: 'ツール',
    extension: '拡張',
  },
  list: {
    source: {
      local: 'ローカルパッケージファイルからインストール',
      github: 'GitHub からインストールする',
      marketplace: 'マーケットプレイスからインストール',
    },
    noInstalled: 'プラグインはインストールされていません',
    notFound: 'プラグインが見つかりません',
  },
  source: {
    github: 'GitHub',
    local: 'ローカルパッケージファイル',
    marketplace: 'マーケットプレイス',
  },
  detailPanel: {
    categoryTip: {
      marketplace: 'マーケットプレイスからインストールされました',
      local: 'ローカルプラグイン',
      debugging: 'デバッグプラグイン',
      github: 'Github からインストールしました',
    },
    operation: {
      info: 'プラグイン情報',
      install: 'インストール',
      viewDetail: '詳細を見る',
      checkUpdate: '更新を確認する',
      update: '更新',
      detail: '詳細',
      remove: '削除',
    },
    toolSelector: {
      descriptionPlaceholder: 'ツールの目的の簡単な説明、例えば、特定の場所の温度を取得すること。',
      paramsTip2: '「自動」がオフのとき、デフォルト値が使用されます。',
      settings: 'ユーザー設定',
      unsupportedContent2: 'バージョンを切り替えるにはクリックしてください。',
      unsupportedContent: 'インストールされたプラグインのバージョンは、このアクションを提供していません。',
      title: 'ツールを追加',
      uninstalledContent: 'このプラグインはローカル/GitHub リポジトリからインストールされます。インストール後にご利用ください。',
      descriptionLabel: 'ツールの説明',
      auto: '自動',
      params: '推論設定',
      uninstalledLink: 'プラグインを管理する',
      placeholder: 'ツールを選択...',
      uninstalledTitle: 'ツールがインストールされていません',
      empty: 'ツールを追加するには「+」ボタンをクリックしてください。複数のツールを追加できます。',
      paramsTip1: 'LLM 推論パラメータを制御します。',
      toolLabel: 'ツール',
      unsupportedTitle: 'サポートされていないアクション',
      toolSetting: 'ツール設定',
      unsupportedMCPTool: '現在選択されているエージェント戦略プラグインのバージョンはMCPツールをサポートしていません。',
    },
    endpointDisableTip: 'エンドポイントを無効にする',
    endpointModalDesc: '設定が完了すると、API エンドポイントを介してプラグインが提供する機能を使用できます。',
    endpointDisableContent: '{{name}}を無効にしますか？',
    endpointModalTitle: 'エンドポイントを設定する',
    endpointDeleteTip: 'エンドポイントを削除',
    modelNum: '{{num}} モデルが含まれています',
    serviceOk: 'サービスは正常です',
    disabled: 'サービスは無効化されています',
    endpoints: 'エンドポイント',
    endpointsTip: 'このプラグインはエンドポイントを介して特定の機能を提供し、現在のワークスペースのために複数のエンドポイントセットを構成できます。',
    configureModel: 'モデルを設定する',
    configureTool: 'ツールを設定する',
    endpointsEmpty: 'エンドポイントを追加するには、\'+\'ボタンをクリックしてください',
    strategyNum: '{{num}} {{strategy}} が含まれています',
    configureApp: 'アプリを設定する',
    endpointDeleteContent: '{{name}}を削除しますか？',
    actionNum: '{{num}} {{action}} が含まれています',
    endpointsDocLink: 'ドキュメントを表示する',
    switchVersion: 'バージョンの切り替え',
    deprecation: {
      fullMessage: 'このプラグインは{{deprecatedReason}}のため非推奨となり、新しいバージョンはリリースされません。代わりに<CustomLink href=\'https://example.com/\'>{{-alternativePluginId}}</CustomLink>をご利用ください。',
      onlyReason: 'このプラグインは{{deprecatedReason}}のため非推奨となり、新しいバージョンはリリースされません。',
      noReason: 'このプラグインは廃止されており、今後更新されることはありません。',
      reason: {
        businessAdjustments: '事業調整',
        ownershipTransferred: '所有権移転',
        noMaintainer: 'メンテナーの不足',
      },
    },
  },
  debugInfo: {
    title: 'デバッグ',
    viewDocs: 'ドキュメントを見る',
  },
  privilege: {
    admins: '管理者',
    noone: '誰もいない',
    whoCanInstall: '誰がプラグインをインストールして管理できますか？',
    whoCanDebug: '誰がプラグインのデバッグを行うことができますか？',
    everyone: 'みんな',
    title: 'プラグインの設定',
  },
  pluginInfoModal: {
    packageName: 'パッケージ',
    release: 'リリース',
    title: 'プラグイン情報',
    repository: 'リポジトリ',
  },
  action: {
    deleteContentRight: 'プラグイン？',
    usedInApps: 'このプラグインは{{num}}のアプリで使用されています。',
    delete: 'プラグインを削除する',
    pluginInfo: 'プラグイン情報',
    deleteContentLeft: '削除しますか',
    checkForUpdates: '更新を確認する',
  },
  installModal: {
    labels: {
      version: 'バージョン',
      package: 'パッケージ',
      repository: 'リポジトリ',
    },
    cancel: 'キャンセル',
    installing: 'インストール中...',
    installedSuccessfully: 'インストールに成功しました',
    installFailedDesc: 'プラグインのインストールに失敗しました。',
    fromTrustSource: '信頼できるソースからのみプラグインをインストールするようにしてください。',
    installedSuccessfullyDesc: 'プラグインは正常にインストールされました。',
    installFailed: 'インストールに失敗しました',
    readyToInstallPackage: '次のプラグインをインストールしようとしています',
    uploadFailed: 'アップロードに失敗しました',
    pluginLoadErrorDesc: 'このプラグインはインストールされません',
    installComplete: 'インストール完了',
    next: '次',
    readyToInstall: '次のプラグインをインストールしようとしています',
    pluginLoadError: 'プラグインの読み込みエラー',
    readyToInstallPackages: '次の{{num}}プラグインをインストールしようとしています',
    close: '閉じる',
    install: 'インストール',
    dropPluginToInstall: 'プラグインパッケージをここにドロップしてインストールします',
    installPlugin: 'プラグインをインストールする',
    back: '戻る',
    uploadingPackage: '{{packageName}}をアップロード中...',
    installWarning: 'このプラグインはインストールを許可されていません。',
  },
  installFromGitHub: {
    installedSuccessfully: 'インストールに成功しました',
    installNote: '信頼できるソースからのみプラグインをインストールするようにしてください。',
    updatePlugin: 'GitHub からプラグインを更新する',
    selectPackage: 'パッケージを選択',
    installFailed: 'インストールに失敗しました',
    selectPackagePlaceholder: 'パッケージを選択してください',
    gitHubRepo: 'GitHub リポジトリ',
    selectVersionPlaceholder: 'バージョンを選択してください',
    uploadFailed: 'アップロードに失敗しました',
    selectVersion: 'バージョンを選択',
    installPlugin: 'GitHub からプラグインをインストールする',
  },
  upgrade: {
    title: 'プラグインをインストールする',
    close: '閉じる',
    upgrading: 'インストール中...',
    description: '次のプラグインをインストールしようとしています',
    successfulTitle: 'インストールに成功しました',
    usedInApps: '{{num}}のアプリで使用されています',
    upgrade: 'インストール',
  },
  error: {
    fetchReleasesError: 'リリースを取得できません。後でもう一度お試しください。',
    inValidGitHubUrl: '無効な GitHub URL です。有効な URL を次の形式で入力してください：https://github.com/owner/repo',
    noReleasesFound: 'リリースは見つかりません。GitHub リポジトリまたは入力 URL を確認してください。',
  },
  marketplace: {
    empower: 'AI 開発をサポートする',
    discover: '探索',
    and: 'と',
    difyMarketplace: 'Dify マーケットプレイス',
    moreFrom: 'マーケットプレイスからのさらなる情報',
    noPluginFound: 'プラグインが見つかりません',
    pluginsResult: '{{num}} 件の結果',
    sortBy: '並べ替え',
    sortOption: {
      mostPopular: '人気順',
      recentlyUpdated: '最近更新順',
      newlyReleased: '新着順',
      firstReleased: 'リリース順',
    },
    viewMore: 'もっと見る',
    verifiedTip: 'このプラグインは Dify によって認証されています',
    partnerTip: 'このプラグインは Dify のパートナーによって認証されています',
  },
  task: {
    installError: '{{errorLength}} プラグインのインストールに失敗しました。表示するにはクリックしてください。',
    installingWithSuccess: '{{installingLength}}個のプラグインをインストール中、{{successLength}}個成功しました。',
    clearAll: 'すべてクリア',
    installedError: '{{errorLength}} プラグインのインストールに失敗しました',
    installingWithError: '{{installingLength}}個のプラグインをインストール中、{{successLength}}件成功、{{errorLength}}件失敗',
    installing: '{{installingLength}}個のプラグインをインストール中、0 個完了。',
  },
  from: 'インストール元',
  install: '{{num}} インストール',
  installAction: 'インストール',
  installFrom: 'インストール元',
  deprecated: '非推奨',
  searchPlugins: '検索プラグイン',
  search: '検索',
  endpointsEnabled: '{{num}} セットのエンドポイントが有効になりました',
  findMoreInMarketplace: 'マーケットプレイスでさらに見つけてください',
  fromMarketplace: 'マーケットプレイスから',
  searchCategories: '検索カテゴリ',
  allCategories: 'すべてのカテゴリ',
  searchTools: '検索ツール...',
  installPlugin: 'プラグインをインストールする',
  searchInMarketplace: 'マーケットプレイスで検索',
  difyVersionNotCompatible: '現在の Dify バージョンはこのプラグインと互換性がありません。最小バージョンは{{minimalDifyVersion}}です。',
  metadata: {
    title: 'プラグイン',
  },
  requestAPlugin: 'プラグインをリクエストする',
  publishPlugins: 'プラグインを公開する',
  auth: {
    saveOnly: '保存のみ',
    oauthClient: 'OAuthクライアント',
    authorizations: '認可',
    useOAuth: 'OAuthを使用してください',
    addApi: 'APIキーを追加してください',
    authRemoved: '認証が削除されました',
    authorizationName: '認証名',
    default: 'デフォルト',
    oauthClientSettings: 'OAuthクライアント設定',
    custom: 'カスタム',
    useApi: 'APIキーを使用してください',
    saveAndAuth: '保存と承認',
    setDefault: 'デフォルトとして設定する',
    setupOAuth: 'OAuthクライアントの設定',
    workspaceDefault: 'ワークスペースのデフォルト',
    useOAuthAuth: 'OAuth認証を使用する',
    useApiAuth: 'APIキー認証設定',
    authorization: '認証',
    addOAuth: 'OAuthを追加する',
    useApiAuthDesc: '認証情報を設定した後、ワークスペース内のすべてのメンバーは、アプリケーションをオーケストレーションする際にこのツールを使用できます。',
    clientInfo: 'このツールプロバイダーにシステムクライアントシークレットが見つからないため、手動で設定する必要があります。redirect_uriには、次を使用してください。',
  },
  autoUpdate: {
    strategy: {
      disabled: {
        name: '無効',
        description: 'プラグインは自動更新されません',
      },
      fixOnly: {
        name: '修正のみ',
        selectedDescription: 'パッチバージョンのみの自動更新',
      },
      latest: {
        name: '最新',
        selectedDescription: '常に最新バージョンに更新してください',
        description: '常に最新バージョンに更新してください',
      },
    },
    upgradeMode: {
      partial: '選択されたもののみ',
      exclude: '選択したものを除外する',
      all: 'すべてを更新する',
    },
    upgradeModePlaceholder: {
      exclude: '選択されたプラグインは自動更新されません',
      partial: '選択されたプラグインのみが自動更新されます。現在選択されているプラグインはないため、プラグインは自動更新されません。',
    },
    operation: {
      clearAll: 'すべてクリア',
      select: 'プラグインを選択する',
    },
    pluginDowngradeWarning: {
      title: 'プラグインのダウングレード',
      downgrade: 'とにかくダウングレードする',
      exclude: '自動更新から除外する',
      description: 'このプラグインは現在、自動更新が有効です。バージョンをダウングレードすると、次回の自動更新中に変更が上書きされる可能性があります。',
    },
    noPluginPlaceholder: {
      noInstalled: 'プラグインがインストールされていません',
      noFound: 'プラグインが見つかりませんでした',
    },
    updateTimeTitle: '更新時刻',
    automaticUpdates: '自動更新',
    updateTime: '更新時刻',
    updateSettings: '設定を更新する',
    nextUpdateTime: '次の自動更新: {{time}}',
    excludeUpdate: '以下の{{num}}プラグインは自動更新されません',
    changeTimezone: 'タイムゾーンを変更するには、<setTimezone>設定</setTimezone>に移動してください。',
    specifyPluginsToUpdate: '更新するプラグインを指定してください',
    partialUPdate: '以下の{{num}}プラグインのみが自動更新されます',
  },
}

export default translation
