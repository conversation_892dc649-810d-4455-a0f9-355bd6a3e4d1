const translation = {
  knowledge: '知識庫',
  documentCount: ' 文件',
  wordCount: ' 千字元',
  appCount: ' 關聯應用',
  createDataset: '建立知識庫',
  createDatasetIntro: '匯入您自己的文字資料或透過 Webhook 實時寫入資料以增強 LLM 的上下文。',
  deleteDatasetConfirmTitle: '要刪除知識庫嗎？',
  deleteDatasetConfirmContent:
    '刪除知識庫是不可逆的。使用者將無法再訪問您的知識庫，所有的提示配置和日誌將被永久刪除。',
  datasetUsedByApp: '這些知識正被一些應用程序使用。應用程序將無法再使用這些知識，所有提示配置和日誌將被永久刪除。',
  datasetDeleted: '知識庫已刪除',
  datasetDeleteFailed: '刪除知識庫失敗',
  didYouKnow: '你知道嗎？',
  intro1: '知識庫可以被整合到 Dify 應用中',
  intro2: '作為上下文',
  intro3: ',',
  intro4: '或可以',
  intro5: '建立',
  intro6: '為獨立的 ChatGPT 外掛釋出使用',
  unavailable: '不可用',
  unavailableTip: '由於 embedding 模型不可用，需要配置預設 embedding 模型',
  datasets: '知識庫',
  datasetsApi: 'API',
  retrieval: {
    semantic_search: {
      title: '向量檢索',
      description: '透過生成查詢嵌入並查詢與其向量表示最相似的文字分段',
    },
    full_text_search: {
      title: '全文檢索',
      description: '索引文件中的所有詞彙，從而允許使用者查詢任意詞彙，並返回包含這些詞彙的文字片段',
    },
    hybrid_search: {
      title: '混合檢索',
      description: '同時執行全文檢索和向量檢索，並應用重排序步驟，從兩類查詢結果中選擇匹配使用者問題的最佳結果，需配置 Rerank 模型 API',
      recommend: '推薦',
    },
    invertedIndex: {
      title: '倒排索引',
      description: '倒排索引是一種用於高效檢索的結構。按術語組織，每個術語指向包含它的文件或網頁',
    },
    change: '更改',
    changeRetrievalMethod: '更改檢索方法',
  },
  docsFailedNotice: '文件無法被索引',
  retry: '重試',
  indexingTechnique: {
    high_quality: '高質量',
    economy: '經濟',
  },
  indexingMethod: {
    semantic_search: '向量',
    full_text_search: '全文',
    hybrid_search: '混合',
    invertedIndex: '倒排索引',
  },
  mixtureHighQualityAndEconomicTip: '混合高質量和經濟知識庫需要重新排序模型。',
  inconsistentEmbeddingModelTip: '如果選定知識庫的嵌入模型不一致，則需要重新排序模型。',
  retrievalSettings: '檢索設置',
  rerankSettings: '重新排序設置',
  weightedScore: {
    title: '加權分數',
    description: '通過調整分配的權重，此重新排序策略決定是優先考慮語義匹配還是關鍵詞匹配。',
    semanticFirst: '語義優先',
    keywordFirst: '關鍵詞優先',
    customized: '自定義',
    semantic: '語義',
    keyword: '關鍵詞',
  },
  nTo1RetrievalLegacy: 'N 對 1 檢索將從 9 月起正式棄用。建議使用最新的多路徑檢索以獲得更好的結果。',
  nTo1RetrievalLegacyLink: '了解更多',
  nTo1RetrievalLegacyLinkText: 'N 對 1 檢索將於 9 月正式棄用。',
  defaultRetrievalTip: '默認情況下，使用多路徑檢索。從多個知識庫中檢索知識，然後重新排名。',
  editExternalAPIConfirmWarningContent: {
    end: 'external knowledge，並且此修改將應用於所有這些 Knowledge。是否確實要保存此更改？',
    front: '此外部知識 API 連結到',
  },
  editExternalAPIFormWarning: {
    end: '外部知識',
    front: '此外部 API 連結到',
  },
  deleteExternalAPIConfirmWarningContent: {
    title: {
      end: '?',
      front: '刪除',
    },
    content: {
      front: '此外部知識 API 連結到',
      end: '外部知識。刪除此 API 將使所有這些 API 失效。是否確實要刪除此 API？',
    },
    noConnectionContent: '您確定要刪除此 API 嗎？',
  },
  selectExternalKnowledgeAPI: {
    placeholder: '選擇外部知識 API',
  },
  connectDatasetIntro: {
    content: {
      link: '瞭解如何創建外部 API',
      front: '要連接到外部知識庫，您需要先創建外部 API。請仔細閱讀並參考',
      end: '.然後找到對應的知識 ID 並在左側的表單中填寫。如果資訊全部正確，點擊連接按鈕后，會自動跳轉到知識庫中的檢索測試。',
    },
    title: '如何連接到外部知識庫',
    learnMore: '瞭解更多資訊',
  },
  connectHelper: {
    helper5: '使用此功能前請小心。',
    helper3: '.我們強烈建議您',
    helper2: '僅支援檢索功能',
    helper4: '閱讀幫助文件',
    helper1: '通過 API 和知識庫 ID 連接到外部知識庫。目前，',
  },
  externalKnowledgeForm: {
    connect: '連接',
    cancel: '取消',
  },
  externalAPIForm: {
    encrypted: {
      end: '科技。',
      front: '您的 API 令牌將使用',
    },
    save: '救',
    cancel: '取消',
    name: '名字',
    apiKey: 'API 金鑰',
    edit: '編輯',
    endpoint: 'API 終端節點',
  },
  externalTag: '外部',
  externalKnowledgeDescription: '知識描述',
  mixtureInternalAndExternalTip: 'Rerank 模型是內部和外部知識的混合所必需的。',
  connectDataset: '連接到外部知識庫',
  learnHowToWriteGoodKnowledgeDescription: '瞭解如何編寫良好的知識描述',
  createExternalAPI: '添加外部知識 API',
  externalAPIPanelTitle: '外部知識 API',
  createNewExternalAPI: '創建新的外部知識 API',
  externalKnowledgeDescriptionPlaceholder: '描述此知識庫中的內容（選擇）',
  allExternalTip: '僅使用外部知識時，用戶可以選擇是否啟用 Rerank 模型。如果未啟用，則檢索到的數據塊將根據分數進行排序。當不同知識庫的檢索策略不一致時，就會不準確。',
  externalKnowledgeIdPlaceholder: '請輸入 Knowledge ID',
  editExternalAPIFormTitle: '編輯外部知識 API',
  externalKnowledgeId: '外部知識 ID',
  externalAPIPanelDescription: '外部知識 API 用於連接到 Dify 外部的知識庫，並從該知識庫中檢索知識。',
  externalAPI: '外部 API',
  editExternalAPITooltipTitle: '關聯知識',
  externalKnowledgeName: '外部知識名稱',
  externalAPIPanelDocumentation: '瞭解如何創建外部知識 API',
  externalKnowledgeNamePlaceholder: '請輸入知識庫的名稱',
  noExternalKnowledge: '目前還沒有外部知識 API，按兩下此處創建',
  chunkingMode: {
    parentChild: '父子',
    general: '常規',
  },
  parentMode: {
    paragraph: '段',
    fullDoc: '完整文件',
  },
  batchAction: {
    delete: '刪除',
    enable: '使',
    disable: '禁用',
    cancel: '取消',
    archive: '檔案',
    selected: '選擇',
  },
  enable: '使',
  documentsDisabled: '已禁用 {{num}} 個文檔 - 處於非活動狀態超過 30 天',
  localDocs: '本地文件',
  preprocessDocument: '{{num}}預處理文件',
  allKnowledge: '所有知識',
  allKnowledgeDescription: '選擇以顯示此工作區中的所有知識。只有 Workspace 擁有者可以管理所有知識。',
  metadata: {
    createMetadata: {
      type: '類型',
      title: '新元數據',
      back: '返回',
      namePlaceholder: '添加元數據名稱',
      name: '名字',
    },
    checkName: {
      empty: '元數據名稱不能為空',
      invalid: '元數據名稱只能包含小寫字母、數字和底線，並且必須以小寫字母開頭',
      tooLong: '元數據名稱不能超過 {{max}} 個字符',
    },
    batchEditMetadata: {
      applyToAllSelectDocumentTip: '自動為所有選定文檔創建上述所有編輯和新元數據，否則編輯元數據將僅適用於具有該元數據的文檔。',
      editDocumentsNum: '編輯 {{num}} 份文件',
      multipleValue: '多重價值',
      applyToAllSelectDocument: '應用於所有選定的文件',
      editMetadata: '編輯元資料',
    },
    selectMetadata: {
      search: '搜尋元數據',
      newAction: '新元數據',
      manageAction: '管理',
    },
    datasetMetadata: {
      rename: '重新命名',
      addMetaData: '添加元數據',
      builtInDescription: '內建的元數據會自動提取和生成。在使用之前必須啟用，且無法編輯。',
      name: '名字',
      disabled: '禁用',
      deleteTitle: '確認刪除',
      values: '{{num}} 值',
      deleteContent: '您確定要刪除元數據 "{{name}}" 嗎？',
      builtIn: '內建的',
      description: '您可以在這裡管理所有的元數據。對所有文件的修改將會同步。',
      namePlaceholder: '元數據名稱',
    },
    documentMetadata: {
      startLabeling: '開始標記',
      documentInformation: '文件資訊',
      technicalParameters: '技術參數',
      metadataToolTip: '元數據作為一個關鍵的過濾器，提高了信息檢索的準確性和相關性。您可以在此處修改和添加此文檔的元數據。',
    },
    metadata: '元數據',
    chooseTime: '選擇一個時間...',
    addMetadata: '添加元數據',
  },
  embeddingModelNotAvailable: '嵌入模型無法使用。',
}

export default translation
