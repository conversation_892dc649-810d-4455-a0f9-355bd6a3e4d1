const translation = {
  title: '知识库设置',
  desc: '在这里，您可以修改此知识库的属性和检索设置',
  form: {
    name: '知识库名称',
    namePlaceholder: '请输入知识库名称',
    nameError: '名称不能为空',
    desc: '知识库描述',
    descInfo: '请写出清楚的文字描述来概述知识库的内容。当从多个知识库中进行选择匹配时，该描述将用作匹配的基础。',
    descPlaceholder: '描述该数据集的内容。详细描述可以让 AI 更快地访问数据集的内容。如果为空，Dify 将使用默认的命中策略。',
    helpText: '学习如何编写一份优秀的数据集描述。',
    descWrite: '了解如何编写更好的知识库描述。',
    permissions: '可见权限',
    permissionsOnlyMe: '只有我',
    permissionsAllMember: '所有团队成员',
    permissionsInvitedMembers: '部分团队成员',
    me: '（你）',
    indexMethod: '索引模式',
    indexMethodHighQuality: '高质量',
    indexMethodHighQualityTip: '调用嵌入模型来处理文档以实现更精确的检索，可以帮助大语言模型生成高质量的回答。',
    upgradeHighQualityTip: '一旦升级为高质量模式，将无法切换回经济模式。',
    indexMethodEconomy: '经济',
    indexMethodEconomyTip: '每个块使用 10 个关键词进行检索，不消耗 tokens，但会降低检索准确性。',
    embeddingModel: 'Embedding 模型',
    embeddingModelTip: '修改 Embedding 模型，请去',
    embeddingModelTipLink: '设置',
    retrievalSetting: {
      title: '检索设置',
      method: '检索方法',
      learnMore: '了解更多',
      description: '关于检索方法。',
      longDescription: '关于检索方法，您可以随时在知识库设置中更改此设置。',
    },
    externalKnowledgeAPI: '外部知识 API',
    externalKnowledgeID: '外部知识库 ID',
    save: '保存',
    retrievalSettings: '检索设置',
    indexMethodChangeToEconomyDisabledTip: '无法从高质量降级为经济',
    searchModel: '搜索模型',
  },
}

export default translation
