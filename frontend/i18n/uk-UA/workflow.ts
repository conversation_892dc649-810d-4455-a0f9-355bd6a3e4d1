const translation = {
  common: {
    undo: 'Скасувати',
    redo: 'Повторити',
    editing: 'Редагування',
    autoSaved: 'Автоматично збережено',
    unpublished: 'Неопубліковано',
    published: 'Опубліковано',
    publish: 'Опублікувати',
    update: 'Оновити',
    run: 'Запустити',
    running: 'Запущено',
    inRunMode: 'У режимі запуску',
    inPreview: 'У режимі попереднього перегляду',
    inPreviewMode: 'У режимі попереднього перегляду',
    preview: 'Попередній перегляд',
    viewRunHistory: 'Переглянути історію запусків',
    runHistory: 'Історія запусків',
    goBackToEdit: 'Повернутися до редактора',
    conversationLog: 'Журнал розмов',
    features: 'Функції',
    debugAndPreview: 'Попередній перегляд',
    restart: 'Перезапустити',
    currentDraft: 'Поточний чернетка',
    currentDraftUnpublished: 'Поточний чернетка неопублікований',
    latestPublished: 'Останнє опубліковане',
    publishedAt: 'Опубліковано о',
    restore: 'Відновити',
    runApp: 'Запустити додаток',
    batchRunApp: 'Пакетний запуск додатку',
    accessAPIReference: 'Доступ до довідника API',
    embedIntoSite: 'Вбудувати на сайт',
    addTitle: 'Додати заголовок...',
    addDescription: 'Додати опис...',
    noVar: 'Без змінної',
    searchVar: 'Пошук змінної',
    variableNamePlaceholder: 'Назва змінної',
    setVarValuePlaceholder: 'Встановити значення змінної',
    needConnectTip: 'Цей крок ні до чого не підключений',
    maxTreeDepth: 'Максимальний ліміт {{depth}} вузлів на гілку',
    workflowProcess: 'Процес робочого потоку',
    notRunning: 'Ще не запущено',
    previewPlaceholder: 'Введіть вміст у поле нижче, щоб розпочати налагодження чат-бота',
    effectVarConfirm: {
      title: 'Видалити змінну',
      content: 'Змінна використовується в інших вузлах. Ви все ще хочете її видалити?',
    },
    insertVarTip: 'Натисніть клавішу \'/\' для швидкого вставлення',
    processData: 'Обробити дані',
    input: 'Вхід',
    output: 'Вихід',
    jinjaEditorPlaceholder: 'Введіть \'/\' або \'{\' для вставлення змінної',
    viewOnly: 'Тільки перегляд',
    showRunHistory: 'Показати історію запусків',
    enableJinja: 'Увімкнути підтримку шаблонів Jinja',
    learnMore: 'Дізнатися більше',
    copy: 'Копіювати',
    duplicate: 'Дублювати',
    pasteHere: 'Вставити сюди',
    pointerMode: 'Режим вказівника',
    handMode: 'Ручний режим',
    model: 'Модель',
    workflowAsTool: 'Робочий потік як інструмент',
    configureRequired: 'Потрібна конфігурація',
    configure: 'Налаштувати',
    manageInTools: 'Керування в інструментах',
    workflowAsToolTip: 'Після оновлення робочого потоку необхідна переконфігурація інструменту.',
    viewDetailInTracingPanel: 'Переглянути деталі',
    importSuccess: 'Успіх імпорту',
    overwriteAndImport: 'Перезапис та імпорт',
    importFailure: 'Помилка імпорту',
    importDSL: 'Імпорт DSL',
    syncingData: 'Синхронізація даних, всього за кілька секунд.',
    chooseDSL: 'Виберіть файл DSL(yml)',
    backupCurrentDraft: 'Резервна поточна чернетка',
    importDSLTip: 'Поточна чернетка буде перезаписана. Експортуйте робочий процес як резервну копію перед імпортом.',
    parallelTip: {
      click: {
        title: 'Натисніть',
        desc: 'щоб додати',
      },
      drag: {
        title: 'Перетягувати',
        desc: 'Щоб підключити',
      },
      limit: 'Паралелізм обмежується {{num}} гілками.',
      depthLimit: 'Обмеження рівня паралельного вкладеності шарів {{num}}',
    },
    disconnect: 'Відключити',
    parallelRun: 'Паралельний біг',
    jumpToNode: 'Перейти до цього вузла',
    addParallelNode: 'Додати паралельний вузол',
    parallel: 'ПАРАЛЕЛЬНИЙ',
    branch: 'ГІЛКА',
    featuresDocLink: 'Дізнатися більше',
    featuresDescription: 'Покращення взаємодії з користувачем веб-додатку',
    fileUploadTip: 'Функції завантаження зображень були оновлені для завантаження файлів.',
    ImageUploadLegacyTip: 'Тепер ви можете створювати змінні типу файлу у початковій формі. У майбутньому ми більше не підтримуватимемо функцію завантаження зображень.',
    importWarning: 'Обережність',
    importWarningDetails: 'Різниця у версіях DSL може впливати на певні функції',
    openInExplore: 'Відкрити в Огляді',
    onFailure: 'Про невдачу',
    addFailureBranch: 'Додано гілку помилки',
    noHistory: 'Без історії',
    loadMore: 'Завантажте більше робочих процесів',
    referenceVar: 'Посилальна змінна',
    exportPNG: 'Експортувати як PNG',
    noExist: 'Такої змінної не існує',
    exitVersions: 'Вихідні версії',
    versionHistory: 'Історія версій',
    publishUpdate: 'Опублікувати оновлення',
    exportImage: 'Експорт зображення',
    exportSVG: 'Експортувати як SVG',
    exportJPEG: 'Експортувати як JPEG',
    addBlock: 'Додати вузол',
    needEndNode: 'Необхідно додати кінцевий вузол',
    needAnswerNode: 'Вузол Відповіді повинен бути доданий',
    tagBound: 'Кількість додатків, що використовують цей тег',
  },
  env: {
    envPanelTitle: 'Змінні середовища',
    envDescription: 'Змінні середовища можуть використовуватися для зберігання приватної інформації та облікових даних. Вони доступні лише для читання і можуть бути відокремлені від файлу DSL під час експорту.',
    envPanelButton: 'Додати змінну',
    modal: {
      title: 'Додати змінну середовища',
      editTitle: 'Редагувати змінну середовища',
      type: 'Тип',
      name: 'Назва',
      namePlaceholder: 'назва середовища',
      value: 'Значення',
      valuePlaceholder: 'значення середовища',
      secretTip: 'Використовується для визначення конфіденційної інформації або даних, з налаштуваннями DSL, сконфігурованими для запобігання витоку.',
      description: 'Опис',
      descriptionPlaceholder: 'Опишіть змінну',
    },
    export: {
      title: 'Експортувати секретні змінні середовища?',
      checkbox: 'Експортувати секретні значення',
      ignore: 'Експортувати DSL',
      export: 'Експортувати DSL з секретними значеннями',
    },
  },
  chatVariable: {
    panelTitle: 'Змінні розмови',
    panelDescription: 'Змінні розмови використовуються для зберігання інтерактивної інформації, яку LLM повинен пам\'ятати, включаючи історію розмови, завантажені файли, вподобання користувача. Вони доступні для читання та запису.',
    docLink: 'Відвідайте нашу документацію, щоб дізнатися більше.',
    button: 'Додати змінну',
    modal: {
      title: 'Додати змінну розмови',
      editTitle: 'Редагувати змінну розмови',
      name: 'Назва',
      namePlaceholder: 'Назва змінної',
      type: 'Тип',
      value: 'Значення за замовчуванням',
      valuePlaceholder: 'Значення за замовчуванням, залиште порожнім, щоб не встановлювати',
      description: 'Опис',
      descriptionPlaceholder: 'Опишіть змінну',
      editInJSON: 'Редагувати в JSON',
      oneByOne: 'Додавати по одному',
      editInForm: 'Редагувати у формі',
      arrayValue: 'Значення',
      addArrayValue: 'Додати значення',
      objectKey: 'Ключ',
      objectType: 'Тип',
      objectValue: 'Значення за замовчуванням',
    },
    storedContent: 'Збережений вміст',
    updatedAt: 'Оновлено ',
  },
  changeHistory: {
    title: 'Історія змін',
    placeholder: 'Ви ще нічого не змінили',
    clearHistory: 'Очистити історію',
    hint: 'Підказка',
    hintText: 'Дії редагування відстежуються в історії змін, яка зберігається на вашому пристрої протягом цієї сесії. Ця історія буде видалена після виходу з редактора.',
    stepBackward_one: '{{count}} крок назад',
    stepBackward_other: '{{count}} кроки назад',
    stepForward_one: '{{count}} крок вперед',
    stepForward_other: '{{count}} кроки вперед',
    sessionStart: 'Початок сесії',
    currentState: 'Поточний стан',
    noteAdd: 'Додано нотатку',
    noteChange: 'Нотатку змінено',
    noteDelete: 'Нотатку видалено',
    nodeTitleChange: 'Заголовок вузла змінено',
    nodeResize: 'Вузол змінив розмір',
    nodePaste: 'Вузол вставлений',
    nodeDelete: 'Вузол видалено',
    nodeDragStop: 'Вузол переміщено',
    edgeDelete: 'Вузол відключено',
    nodeChange: 'Вузол змінився',
    nodeAdd: 'Додано вузол',
    nodeDescriptionChange: 'Опис вузла змінено',
    nodeConnect: 'Вузол підключено',
  },
  errorMsg: {
    fieldRequired: '{{field}} є обов\'язковим',
    authRequired: 'Потрібна авторизація',
    invalidJson: '{{field}} є недійсним JSON',
    fields: {
      variable: 'Назва змінної',
      variableValue: 'Значення змінної',
      code: 'Код',
      model: 'Модель',
      rerankModel: 'Модель повторного ранжування',
      visionVariable: 'Змінна зору',
    },
    invalidVariable: 'Недійсна змінна',
    rerankModelRequired: 'Перед увімкненням Rerank Model, будь ласка, підтвердьте, що модель успішно налаштована в налаштуваннях.',
    noValidTool: '{{field}} не вибрано дійсного інструменту',
    toolParameterRequired: '{{field}}: параметр [{{param}}] обов\'язковий',
  },
  singleRun: {
    testRun: 'Тестовий запуск',
    startRun: 'Почати запуск',
    running: 'Запущено',
    testRunIteration: 'Ітерація тестового запуску',
    back: 'Назад',
    iteration: 'Ітерація',
    loop: 'Петля',
  },
  tabs: {
    'tools': 'Інструменти',
    'allTool': 'Усі',
    'builtInTool': 'Вбудовані',
    'customTool': 'Користувацькі',
    'workflowTool': 'Робочий потік',
    'question-understand': 'Розуміння питань',
    'logic': 'Логіка',
    'transform': 'Трансформація',
    'utilities': 'Утиліти',
    'noResult': 'Нічого не знайдено',
    'searchTool': 'Інструмент пошуку',
    'plugin': 'Плагін',
    'agent': 'Стратегія агента',
    'blocks': 'Вузли',
    'searchBlock': 'Пошуковий вузол',
    'addAll': 'Додати все',
    'allAdded': 'Всі додані',
  },
  blocks: {
    'start': 'Початок',
    'end': 'Кінець',
    'answer': 'Відповідь',
    'llm': 'LLM',
    'knowledge-retrieval': 'Отримання знань',
    'question-classifier': 'Класифікатор питань',
    'if-else': 'ЯКЩО/ІНАКШЕ',
    'code': 'Код',
    'template-transform': 'Шаблон',
    'http-request': 'HTTP-запит',
    'variable-assigner': 'Присвоювач змінних',
    'variable-aggregator': 'Агрегатор змінних',
    'assigner': 'Призначувач змінних',
    'iteration-start': 'Початок ітерації',
    'iteration': 'Ітерація',
    'parameter-extractor': 'Екстрактор параметрів',
    'document-extractor': 'Екстрактор документів',
    'list-operator': 'Оператор списку',
    'agent': 'Агент',
    'loop-start': 'Початок циклу',
    'loop': 'Петля',
    'loop-end': 'Вихід з циклу',
  },
  blocksAbout: {
    'start': 'Визначте початкові параметри для запуску робочого потоку',
    'end': 'Визначте кінець і тип результату робочого потоку',
    'answer': 'Визначте зміст відповіді у чаті',
    'llm': 'Виклик великих мовних моделей для відповіді на запитання або обробки природної мови',
    'knowledge-retrieval': 'Дозволяє виконувати запити текстового вмісту, пов\'язаного із запитаннями користувача, з бази знань',
    'question-classifier': 'Визначте умови класифікації запитань користувачів, LLM може визначати, як розвивається розмова на основі опису класифікації',
    'if-else': 'Дозволяє розділити робочий потік на дві гілки на основі умов if/else',
    'code': 'Виконайте фрагмент коду Python або NodeJS для реалізації користувацької логіки',
    'template-transform': 'Перетворіть дані на рядок за допомогою синтаксису шаблону Jinja',
    'http-request': 'Дозволяє відправляти серверні запити через протокол HTTP',
    'variable-assigner': 'Агрегує змінні з кількох гілок у одну змінну для уніфікованої конфігурації кінцевих вузлів.',
    'assigner': 'Вузол призначення змінних використовується для присвоєння значень записуваним змінним (таким як змінні розмови).',
    'variable-aggregator': 'Агрегує змінні з кількох гілок у одну змінну для уніфікованої конфігурації кінцевих вузлів.',
    'iteration': 'Виконувати кілька кроків на об\'єкті списку, поки не буде виведено всі результати.',
    'parameter-extractor': 'Використовуйте LLM для вилучення структурованих параметрів з природної мови для викликів інструментів або HTTP-запитів.',
    'document-extractor': 'Використовується для аналізу завантажених документів у текстовий контент, який легко зрозумілий LLM.',
    'list-operator': 'Використовується для фільтрації або сортування вмісту масиву.',
    'agent': 'Виклик великих мовних моделей для відповідей на запитання або обробки природної мови',
    'loop': 'Виконуйте цикл логіки, поки не буде виконано умову завершення або досягнуто максимальну кількість ітерацій.',
    'loop-end': 'Еквівалентно "перерві". Цей вузол не має елементів конфігурації. Коли тіло циклу досягає цього вузла, цикл завершується.',
  },
  operator: {
    zoomIn: 'Збільшити',
    zoomOut: 'Зменшити',
    zoomTo50: 'Збільшити до 50%',
    zoomTo100: 'Збільшити до 100%',
    zoomToFit: 'Збільшити для підгонки',
  },
  panel: {
    userInputField: 'Поле введення користувача',
    helpLink: 'Посилання на допомогу',
    about: 'Про',
    createdBy: 'Створено ',
    nextStep: 'Наступний крок',
    runThisStep: 'Запустити цей крок',
    checklist: 'Контрольний список',
    checklistTip: 'Переконайтеся, що всі проблеми вирішені перед публікацією',
    checklistResolved: 'Всі проблеми вирішені',
    change: 'Змінити',
    optional: '(необов\'язково)',
    moveToThisNode: 'Перемістіть до цього вузла',
    organizeBlocks: 'Організуйте вузли',
    changeBlock: 'Змінити вузол',
    selectNextStep: 'Виберіть наступний крок',
    addNextStep: 'Додайте наступний крок у цей робочий процес',
    minimize: 'Вийти з повноекранного режиму',
    maximize: 'Максимізувати полотно',
  },
  nodes: {
    common: {
      outputVars: 'Змінні виходу',
      insertVarTip: 'Вставити змінну',
      memory: {
        memory: 'Пам\'ять',
        memoryTip: 'Налаштування пам\'яті чату',
        windowSize: 'Розмір вікна',
        conversationRoleName: 'Назва ролі у розмові',
        user: 'Префікс користувача',
        assistant: 'Префікс помічника',
      },
      memories: {
        title: 'Спогади',
        tip: 'Пам\'ять чату',
        builtIn: 'Вбудовано',
      },
      errorHandle: {
        none: {
          title: 'Ніхто',
          desc: 'Вузол припинить роботу, якщо виникне виняток і не буде оброблений',
        },
        defaultValue: {
          title: 'Значення за замовчуванням',
          desc: 'При виникненні помилки вкажіть статичний вміст виводу.',
          tip: 'У разі помилки поверне нижче значення.',
          output: 'Вивести значення за замовчуванням',
          inLog: 'Виняток вузла, що виводить відповідно до значень за замовчуванням.',
        },
        failBranch: {
          desc: 'Коли виникає помилка, він виконає гілку винятку',
          customize: 'Перейдіть до полотна, щоб налаштувати логіку fail branch.',
          inLog: 'Виняток вузла, автоматично виконає гілку fail. Вихідні дані вузла повернуть тип помилки та повідомлення про помилку та передадуть їх до низхідного потоку.',
          title: 'Гілка невдачі',
          customizeTip: 'Коли гілка fail активована, винятки, викликані вузлами, не завершать процес. Замість цього він автоматично виконає попередньо визначену гілку помилки, дозволяючи вам гнучко надавати повідомлення про помилки, звіти, виправлення або пропускати дії.',
        },
        partialSucceeded: {
          tip: 'У процесі є вузли {{num}}, які працюють ненормально, будь ласка, перейдіть до трасування, щоб перевірити логи.',
        },
        title: 'Обробка помилок',
        tip: 'Стратегія обробки винятків, що спрацьовує, коли вузол стикається з винятком.',
      },
      retry: {
        retry: 'Повторити',
        retryOnFailure: 'повторити спробу в разі невдачі',
        retryInterval: 'Інтервал повторних спроб',
        retrying: 'Спроби...',
        retryFailed: 'Повторна спроба не вдалася',
        times: 'Разів',
        ms: 'МС',
        retries: '{{num}} Спроб',
        maxRetries: 'Максимальна кількість повторних спроб',
        retrySuccessful: 'Повторна спроба успішна',
        retryFailedTimes: '{{times}} повторні спроби не вдалися',
        retryTimes: 'Повторіть спробу {{times}} у разі невдачі',
      },
      typeSwitch: {
        input: 'Вхідне значення',
        variable: 'Використовуйте змінну',
      },
    },
    start: {
      required: 'обов\'язковий',
      inputField: 'Поле введення',
      builtInVar: 'Вбудовані змінні',
      outputVars: {
        query: 'Введення користувача',
        memories: {
          des: 'Історія розмов',
          type: 'тип повідомлення',
          content: 'вміст повідомлення',
        },
        files: 'Список файлів',
      },
      noVarTip: 'Встановіть вхідні дані, які можуть бути використані у робочому потоці',
    },
    end: {
      outputs: 'Виходи',
      output: {
        type: 'тип виходу',
        variable: 'змінна виходу',
      },
      type: {
        'none': 'Немає',
        'plain-text': 'Простий текст',
        'structured': 'Структурований',
      },
    },
    answer: {
      answer: 'Відповідь',
      outputVars: 'Змінні виходу',
    },
    llm: {
      model: 'модель',
      variables: 'змінні',
      context: 'контекст',
      contextTooltip: 'Ви можете імпортувати знання як контекст',
      notSetContextInPromptTip: 'Щоб увімкнути функцію контексту, заповніть змінну контексту в PROMPT.',
      prompt: 'prompt',
      roleDescription: {
        system: 'Дайте високорівневі інструкції для розмови',
        user: 'Надайте інструкції, запити або будь-який текстовий вхід для моделі',
        assistant: 'Відповіді моделі на основі повідомлень користувача',
      },
      addMessage: 'Додати повідомлення',
      vision: 'бачення',
      files: 'Файли',
      resolution: {
        name: 'Роздільна здатність',
        high: 'Висока',
        low: 'Низька',
      },
      outputVars: {
        output: 'Генерований вміст',
        usage: 'Інформація про використання моделі',
      },
      singleRun: {
        variable: 'Змінна',
      },
      sysQueryInUser: 'sys.query у повідомленні користувача є обов\'язковим',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Будь ласка, завершіть редагування поточного поля перед збереженням схемы.',
        },
        import: 'Імпорт з JSON',
        instruction: 'Інструкція',
        descriptionPlaceholder: 'Додати опис',
        addField: 'Додати поле',
        promptTooltip: 'Перетворіть текстовий опис у стандартизовану структуру JSON Schema.',
        resultTip: 'Ось згенерований результат. Якщо вас не влаштовує, ви можете повернутися назад і змінити свій запит.',
        promptPlaceholder: 'Опишіть вашу схему JSON...',
        generating: 'Генерація JSON-схеми...',
        back: 'Назад',
        generatedResult: 'Згенерований результат',
        fieldNamePlaceholder: 'Назва поля',
        addChildField: 'Додати поле дитини',
        apply: 'Застосувати',
        regenerate: 'Перегенерувати',
        resetDefaults: 'Скинути',
        generateJsonSchema: 'Згенерувати JSON Схему',
        showAdvancedOptions: 'Показати розширені налаштування',
        required: 'необхідно',
        generationTip: 'Ви можете використовувати природну мову, щоб швидко створити JSON-схему.',
        stringValidations: 'Валідність рядків',
        generate: 'Генерувати',
        title: 'Структурована схема виходу',
        doc: 'Дізнайтеся більше про структурований вихід',
      },
    },
    knowledgeRetrieval: {
      queryVariable: 'Змінна запиту',
      knowledge: 'Знання',
      outputVars: {
        output: 'Відновлені сегментовані дані',
        content: 'Сегментований вміст',
        title: 'Сегментований заголовок',
        icon: 'Сегментована піктограма',
        url: 'Сегментована URL',
        metadata: 'Інші метадані',
      },
      metadata: {
        options: {
          disabled: {
            title: 'Вимкнено',
            subTitle: 'Не включаючи фільтрацію метаданих',
          },
          automatic: {
            title: 'Автоматичний',
            subTitle: 'Автоматично генерувати умови фільтрації метаданих на основі запиту користувача.',
            desc: 'Автоматично генерувати умови фільтрації метаданих на основі змінної запиту.',
          },
          manual: {
            subTitle: 'Вручну додайте умови фільтрації метаданих',
            title: 'Посібник',
          },
        },
        panel: {
          search: 'Пошукова метаінформація',
          datePlaceholder: 'Виберіть час...',
          title: 'Умови фільтрації метаданих',
          placeholder: 'Введіть значення',
          conditions: 'Умови',
          select: 'Виберіть змінну...',
          add: 'Додати умову',
        },
        title: 'Фільтрація метаданих',
      },
    },
    http: {
      inputVars: 'Вхідні змінні',
      api: 'API',
      apiPlaceholder: 'Введіть URL, введіть ‘/’, щоб вставити змінну',
      notStartWithHttp: 'API має починатися з http:// або https://',
      key: 'Ключ',
      value: 'Значення',
      bulkEdit: 'Масове редагування',
      keyValueEdit: 'Редагування ключ-значення',
      headers: 'Заголовки',
      params: 'Параметри',
      body: 'Тіло',
      outputVars: {
        body: 'Зміст відповіді',
        statusCode: 'Код стану відповіді',
        headers: 'Список заголовків відповіді у форматі JSON',
        files: 'Список файлів',
      },
      authorization: {
        'authorization': 'Авторизація',
        'authorizationType': 'Тип авторизації',
        'no-auth': 'Немає',
        'api-key': 'API-ключ',
        'auth-type': 'Тип аутентифікації',
        'basic': 'Базовий',
        'bearer': 'Bearer',
        'custom': 'Користувацький',
        'api-key-title': 'API-ключ',
        'header': 'Заголовок',
      },
      insertVarPlaceholder: 'введіть \'/\', щоб вставити змінну',
      timeout: {
        title: 'Тайм-аут',
        connectLabel: 'Тайм-аут підключення',
        connectPlaceholder: 'Введіть тайм-аут підключення в секундах',
        readLabel: 'Тайм-аут читання',
        readPlaceholder: 'Введіть тайм-аут читання в секундах',
        writeLabel: 'Тайм-аут запису',
        writePlaceholder: 'Введіть тайм-аут запису в секундах',
      },
      type: 'Тип',
      binaryFileVariable: 'Змінна двійкового файлу',
      extractListPlaceholder: 'Введіть індекс елемента списку, введіть \'/\' вставити змінну',
      curl: {
        title: 'Імпорт з cURL',
        placeholder: 'Вставте сюди рядок cURL',
      },
      verifySSL: {
        title: 'Перевірити SSL сертифікат',
        warningTooltip: 'Вимкнення перевірки SSL не рекомендується для виробничих середовищ. Це слід використовувати лише в розробці або тестуванні, оскільки це робить з\'єднання вразливим до загроз безпеці, таких як атаки «людина посередині».',
      },
    },
    code: {
      inputVars: 'Вхідні змінні',
      outputVars: 'Змінні виходу',
      advancedDependencies: 'Розширені залежності',
      advancedDependenciesTip: 'Додайте тут деякі попередньо завантажені залежності, які потребують більше часу для споживання або не є за замовчуванням вбудованими',
      searchDependencies: 'Шукати залежності',
      syncFunctionSignature: 'Синхронізувати підпис функції з кодом',
    },
    templateTransform: {
      inputVars: 'Вхідні змінні',
      code: 'Код',
      codeSupportTip: 'Підтримує лише Jinja2',
      outputVars: {
        output: 'Трансформований вміст',
      },
    },
    ifElse: {
      if: 'Якщо',
      else: 'Інакше',
      elseDescription: 'Використовується для визначення логіки, яка має бути виконана, коли умова if не виконана.',
      and: 'і',
      or: 'або',
      operator: 'Оператор',
      notSetVariable: 'Будь ласка, спочатку встановіть змінну',
      comparisonOperator: {
        'contains': 'містить',
        'not contains': 'не містить',
        'start with': 'починається з',
        'end with': 'закінчується на',
        'is': 'є',
        'is not': 'не є',
        'empty': 'порожній',
        'not empty': 'не порожній',
        'null': 'є null',
        'not null': 'не є null',
        'regex match': 'Регулярний вираз збігу',
        'in': 'В',
        'all of': 'Всі з',
        'exists': 'Існує',
        'not exists': 'не існує',
        'not in': 'Не в',
        'after': 'після',
        'before': 'раніше',
      },
      enterValue: 'Введіть значення',
      addCondition: 'Додати умову',
      conditionNotSetup: 'Умова НЕ налаштована',
      selectVariable: 'Виберіть змінну...',
      optionName: {
        audio: 'Аудіо',
        doc: 'Док',
        video: 'Відео',
        localUpload: 'Локальне завантаження',
        image: 'Образ',
        url: 'URL-адреса',
      },
      select: 'Виберіть',
      addSubVariable: 'Підзмінна',
      condition: 'Умова',
    },
    variableAssigner: {
      title: 'Присвоєння змінних',
      outputType: 'Тип виходу',
      varNotSet: 'Змінна не встановлена',
      noVarTip: 'Додайте змінні для присвоєння',
      type: {
        string: 'Рядок',
        number: 'Число',
        object: 'Об\'єкт',
        array: 'Масив',
      },
      aggregationGroup: 'Група агрегації',
      aggregationGroupTip: 'Увімкнення цієї функції дозволяє агрегатору змінних агрегувати кілька наборів змінних.',
      addGroup: 'Додати групу',
      outputVars: {
        varDescribe: 'Вихід {{groupName}}',
      },
      setAssignVariable: 'Встановити змінну присвоєння',
    },
    assigner: {
      'assignedVariable': 'Призначена Змінна',
      'writeMode': 'Режим Запису',
      'writeModeTip': 'Коли ПРИЗНАЧЕНА ЗМІННА є масивом, режим додавання додає в кінець.',
      'over-write': 'Перезаписати',
      'append': 'Додати',
      'plus': 'Плюс',
      'clear': 'Очистити',
      'setVariable': 'Встановити Змінну',
      'variable': 'Змінна',
      'operations': {
        'clear': 'Ясний',
        'set': 'Встановити',
        'title': 'Операція',
        'append': 'Додати',
        '-=': '-=',
        'over-write': 'Перезаписати',
        'overwrite': 'Перезаписати',
        '/=': '/=',
        '+=': '+=',
        '*=': '*=',
        'extend': 'Розширити',
        'remove-last': 'Видалити останнє',
        'remove-first': 'Видалити перший',
      },
      'selectAssignedVariable': 'Виберіть призначену змінну...',
      'noAssignedVars': 'Немає доступних призначених змінних',
      'noVarTip': 'Натисніть кнопку «+», щоб додати змінні',
      'assignedVarsDescription': 'Призначені змінні мають бути доступними для запису, такими як змінні розмови.',
      'variables': 'Змінні',
      'varNotSet': 'Змінна НЕ встановлена',
      'setParameter': 'Встановити параметр...',
    },
    tool: {
      inputVars: 'Вхідні змінні',
      outputVars: {
        text: 'генерований вміст інструменту',
        files: {
          title: 'файли, генеровані інструментом',
          type: 'Тип підтримки. Наразі підтримуються лише зображення',
          transfer_method: 'Метод передачі. Значення - remote_url або local_file',
          url: 'URL зображення',
          upload_file_id: 'ID завантаженого файлу',
        },
        json: 'JSON, згенерований інструментом',
      },
      authorize: 'Уповноважити',
      settings: 'Налаштування',
      insertPlaceholder2: 'вставте змінну',
      insertPlaceholder1: 'Введіть або натисніть',
    },
    questionClassifiers: {
      model: 'модель',
      inputVars: 'Вхідні змінні',
      outputVars: {
        className: 'Назва класу',
        usage: 'Інформація про використання моделі',
      },
      class: 'Клас',
      classNamePlaceholder: 'Напишіть назву вашого класу',
      advancedSetting: 'Розширене налаштування',
      topicName: 'Назва теми',
      topicPlaceholder: 'Напишіть назву вашої теми',
      addClass: 'Додати клас',
      instruction: 'Інструкція',
      instructionTip: 'Введіть додаткові інструкції, щоб допомогти класифікатору запитань краще зрозуміти, як категоризувати запитання.',
      instructionPlaceholder: 'Напишіть вашу інструкцію',
    },
    parameterExtractor: {
      inputVar: 'Вхідна змінна',
      outputVars: {
        isSuccess: 'Є успіх. У разі успіху значення 1, у разі невдачі значення 0.',
        errorReason: 'Причина помилки',
        usage: 'Інформація про використання моделі',
      },
      extractParameters: 'Витягти параметри',
      importFromTool: 'Імпорт з інструментів',
      addExtractParameter: 'Додати параметр витягування',
      addExtractParameterContent: {
        name: 'Ім\'я',
        namePlaceholder: 'Ім\'я параметра витягування',
        type: 'Тип',
        typePlaceholder: 'Тип параметра витягування',
        description: 'Опис',
        descriptionPlaceholder: 'Опис параметра витягування',
        required: 'Обов\'язковий',
        requiredContent: 'Обов\'язковий використовується лише як посилання для інференції моделі і не для обов\'язкової валідації вихідного параметра.',
      },
      extractParametersNotSet: 'Параметри витягування не налаштовані',
      instruction: 'Інструкція',
      instructionTip: 'Введіть додаткові інструкції, щоб допомогти екстрактору параметрів зрозуміти, як витягувати параметри.',
      advancedSetting: 'Розширене налаштування',
      reasoningMode: 'Режим інференції',
      reasoningModeTip: 'Ви можете вибрати відповідний режим інференції залежно від здатності моделі реагувати на інструкції щодо викликів функцій або запитів.',
    },
    iteration: {
      deleteTitle: 'Видалити вузол ітерації?',
      deleteDesc: 'Видалення вузла ітерації видалить усі дочірні вузли',
      input: 'Вхід',
      output: 'Змінні виходу',
      iteration_one: '{{count}} Ітерація',
      iteration_other: '{{count}} Ітерацій',
      currentIteration: 'Поточна ітерація',
      ErrorMethod: {
        operationTerminated: 'Припинено',
        continueOnError: 'Продовжити після помилки',
        removeAbnormalOutput: 'видалити-ненормальний-вивід',
      },
      error_one: '{{count}} Помилка',
      comma: ',',
      MaxParallelismTitle: 'Максимальна паралельність',
      parallelModeUpper: 'ПАРАЛЕЛЬНИЙ РЕЖИМ',
      error_other: '{{count}} Помилки',
      parallelMode: 'Паралельний режим',
      parallelModeEnableTitle: 'Увімкнено паралельний режим',
      errorResponseMethod: 'Метод реагування на помилку',
      parallelPanelDesc: 'У паралельному режимі завдання в ітерації підтримують паралельне виконання.',
      parallelModeEnableDesc: 'У паралельному режимі завдання всередині ітерацій підтримують паралельне виконання. Ви можете налаштувати це на панелі властивостей праворуч.',
      MaxParallelismDesc: 'Максимальний паралелізм використовується для контролю числа завдань, що виконуються одночасно за одну ітерацію.',
      answerNodeWarningDesc: 'Попередження в паралельному режимі: вузли відповідей, призначення змінних розмови та постійні операції читання/запису в межах ітерацій можуть спричинити винятки.',
    },
    note: {
      editor: {
        large: 'Великий',
        bold: 'Жирний',
        openLink: 'Відкривати',
        small: 'Малий',
        link: 'Посилання',
        italic: 'Курсив',
        placeholder: 'Напишіть свою замітку...',
        strikethrough: 'Закреслені',
        medium: 'Середнє',
        showAuthor: 'Показати автора',
        bulletList: 'Маркований список',
        enterUrl: 'Введіть URL-адресу...',
        unlink: 'Від\'єднати',
        invalidUrl: 'Невірна URL-адреса',
      },
      addNote: 'Додати примітку',
    },
    docExtractor: {
      outputVars: {
        text: 'Витягнутий текст',
      },
      learnMore: 'Дізнатися більше',
      inputVar: 'Вхідна змінна',
      supportFileTypes: 'Типи файлів підтримки: {{types}}.',
    },
    listFilter: {
      outputVars: {
        last_record: 'Останній запис',
        first_record: 'Перший запис',
        result: 'Результат фільтра',
      },
      desc: 'СПАДАННЯМ',
      filterCondition: 'Стан фільтра',
      inputVar: 'Вхідна змінна',
      asc: 'ЦНАП',
      filterConditionKey: 'Клавіша умови фільтра',
      limit: 'Зверху N',
      selectVariableKeyPlaceholder: 'Виберіть ключ підзмінної',
      orderBy: 'Замовити по',
      filterConditionComparisonOperator: 'Оператор порівняння умов фільтра',
      filterConditionComparisonValue: 'Значення умови фільтра',
      extractsCondition: 'Витягніть елемент N',
    },
    agent: {
      strategy: {
        selectTip: 'Виберіть агентську стратегію',
        tooltip: 'Різні агентські стратегії визначають, як система планує та виконує багатоетапні виклики інструментів',
        configureTipDesc: 'Після налаштування агентної стратегії цей вузол автоматично завантажить решту конфігурацій. Стратегія вплине на механізм багатоступінчастого інструментального міркування.',
        label: 'Агентична стратегія',
        configureTip: 'Будь ласка, налаштуйте агентичну стратегію.',
        searchPlaceholder: 'Стратегія пошукового агента',
        shortLabel: 'Стратегія',
      },
      pluginInstaller: {
        install: 'Інсталювати',
        installing: 'Установки',
      },
      modelNotInMarketplace: {
        desc: 'Ця модель встановлюється з локального репозиторію або репозиторію GitHub. Будь ласка, використовуйте після встановлення.',
        title: 'Модель не встановлена',
        manageInPlugins: 'Керування в плагінах',
      },
      modelNotSupport: {
        title: 'Непідтримувана модель',
        desc: 'Встановлена версія плагіна не передбачає цю модель.',
        descForVersionSwitch: 'Встановлена версія плагіна не передбачає цю модель. Натисніть, щоб змінити версію.',
      },
      modelSelectorTooltips: {
        deprecated: 'Ця модель вважається застарілою',
      },
      outputVars: {
        files: {
          upload_file_id: 'Завантажити ідентифікатор файлу',
          transfer_method: 'Спосіб переказу. Цінність remote_url або local_file',
          type: 'Тип підтримки. Тепер підтримка тільки зображення',
          url: 'URL-адреса зображення',
          title: 'Файли, створені агентом',
        },
        text: 'Контент, створений агентом',
        json: 'Агент згенерував JSON',
      },
      checkList: {
        strategyNotSelected: 'Стратегію не обрано',
      },
      installPlugin: {
        cancel: 'Скасувати',
        title: 'Встановити плагін',
        desc: 'Про встановлення наступного плагіна',
        changelog: 'Журнал змін',
        install: 'Інсталювати',
      },
      strategyNotSet: 'Агентська стратегія Не встановлено',
      strategyNotFoundDesc: 'Встановлена версія плагіна не забезпечує цю стратегію.',
      notAuthorized: 'Не авторизовано',
      pluginNotInstalled: 'Цей плагін не встановлено',
      linkToPlugin: 'Посилання на плагіни',
      configureModel: 'Налаштуйте модель',
      toolNotInstallTooltip: '{{tool}} не встановлено',
      maxIterations: 'Максимальна кількість ітерацій',
      pluginNotFoundDesc: 'Цей плагін встановлюється з GitHub. Будь ласка, перейдіть до Плагіни для перевстановлення',
      modelNotInstallTooltip: 'Дана модель не встановлена',
      unsupportedStrategy: 'Стратегія без підтримки',
      learnMore: 'Дізнатися більше',
      tools: 'Інструмент',
      strategyNotInstallTooltip: '{{strategy}} не встановлено',
      toolbox: 'ящик для інструментів',
      toolNotAuthorizedTooltip: '{{tool}} Не авторизовано',
      model: 'модель',
      pluginNotInstalledDesc: 'Цей плагін встановлюється з GitHub. Будь ласка, перейдіть до Плагіни для перевстановлення',
      modelNotSelected: 'Модель не обрана',
      strategyNotFoundDescAndSwitchVersion: 'Встановлена версія плагіна не забезпечує цю стратегію. Натисніть, щоб змінити версію.',
      parameterSchema: 'Схема параметрів',
      clickToViewParameterSchema: 'Натисніть, щоб переглянути схему параметрів',
    },
    loop: {
      ErrorMethod: {
        operationTerminated: 'Припинено',
        removeAbnormalOutput: 'Видалити ненормальний вихід',
        continueOnError: 'Продовжити незважаючи на помилку',
      },
      loop_one: '{{count}} Цикл',
      exitConditionTip: 'Вузол циклу потребує принаймні однієї умови виходу',
      error_other: '{{count}} Помилок',
      setLoopVariables: 'Встановіть змінні в межах області видимості циклу',
      loopVariables: 'Циклічні змінні',
      currentLoopCount: 'Поточна кількість циклів: {{count}}',
      totalLoopCount: 'Загальна кількість циклів: {{count}}',
      loop_other: '{{count}} Циклів',
      error_one: '{{count}} Помилка',
      currentLoop: 'Поточна петля',
      breakCondition: 'Умова завершення циклу',
      deleteDesc: 'Видалення вузла циклу призведе до видалення всіх дочірніх вузлів.',
      breakConditionTip: 'Тільки змінні в циклах з умовами завершення та змінні розмови можуть бути використані.',
      initialLoopVariables: 'Початкові змінні циклу',
      finalLoopVariables: 'Заключні змінні циклу',
      input: 'Введення',
      errorResponseMethod: 'Метод відповіді на помилку',
      output: 'Вихідна змінна',
      variableName: 'Змінна Ім\'я',
      comma: ',',
      inputMode: 'Режим введення',
      loopNode: 'Циклічний вузол',
      loopMaxCountError: 'Будь ласка, введіть дійсне максимальне значення циклу, яке коливається від 1 до {{maxCount}}',
      deleteTitle: 'Видалити вузол циклу?',
      loopMaxCount: 'Максимальна кількість циклів',
    },
  },
  tracing: {
    stopBy: 'Зупинено користувачем {{user}}',
  },
  variableReference: {
    conversationVars: 'Змінні розмови',
    noVarsForOperation: 'Немає доступних змінних для призначення з обраною операцією.',
    assignedVarsDescription: 'Призначені змінні мають бути доступними для запису, такими як',
    noAssignedVars: 'Немає доступних призначених змінних',
    noAvailableVars: 'Немає доступних змінних',
  },
  versionHistory: {
    filter: {
      onlyShowNamedVersions: 'Показуйте лише названі версії',
      reset: 'Скинути фільтр',
      all: 'Усе',
      onlyYours: 'Тільки твоє',
      empty: 'Не знайдено відповідну історію версій',
    },
    editField: {
      titleLengthLimit: 'Заголовок не може перевищувати {{limit}} символів',
      releaseNotes: 'Примітки до випуску',
      title: 'Назва',
      releaseNotesLengthLimit: 'Примітки до випуску не можуть перевищувати {{limit}} символів',
    },
    action: {
      restoreFailure: 'Не вдалося відновити версію',
      updateSuccess: 'Версія оновлена',
      deleteFailure: 'Не вдалося видалити версію',
      deleteSuccess: 'Версія видалена',
      restoreSuccess: 'Версія відновлена',
      updateFailure: 'Не вдалося оновити версію',
    },
    defaultName: 'Без назви версія',
    restorationTip: 'Після відновлення версії нинішній проект буде перезаписано.',
    title: 'Версії',
    currentDraft: 'Поточний проект',
    deletionTip: 'Видалення є незворотнім, будь ласка, підтвердіть.',
    releaseNotesPlaceholder: 'Опишіть, що змінилося',
    editVersionInfo: 'Редагувати інформацію про версію',
    nameThisVersion: 'Назвіть цю версію',
    latest: 'Останні новини',
  },
  debug: {
    noData: {
      runThisNode: 'Запустіть цей вузол',
      description: 'Результати останнього запуску будуть відображені тут',
    },
    variableInspect: {
      trigger: {
        stop: 'Зупинись бігти',
        normal: 'Перевірка змінних',
        clear: 'Чіткий',
        cached: 'Переглянути кешовані змінні',
        running: 'Кешування статусу виконання',
      },
      systemNode: 'Система',
      view: 'Переглянути журнал',
      title: 'Перевірка змінних',
      edited: 'Редагований',
      emptyLink: 'Дізнайтеся більше',
      clearNode: 'Очистити кешовану змінну',
      envNode: 'Навколишнє середовище',
      reset: 'Скинути до значення останнього запуску',
      clearAll: 'Скиньте все',
      chatNode: 'Розмова',
      resetConversationVar: 'Скинути змінну розмови на значення за замовчуванням',
      emptyTip: 'Після переходу через вузол на полотні або виконання вузла поетапно, ви можете переглянути поточне значення змінної вузла у Перевірці змінних.',
    },
    lastRunTab: 'Останній запуск',
    settingsTab: 'Налаштування',
  },
}

export default translation
