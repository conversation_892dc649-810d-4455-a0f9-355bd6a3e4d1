const translation = {
  title: 'Werkzeuge',
  createCustomTool: 'Eigenes Werkzeug erstellen',
  type: {
    all: 'Alle',
    builtIn: 'Integriert',
    custom: 'Benutzerdefiniert',
    workflow: 'Arbeitsablauf',
  },
  contribute: {
    line1: 'Ich interessiere mich dafür, ',
    line2: 'Werkzeuge zu Dify beizutragen.',
    viewGuide: 'Leitfaden anzeigen',
  },
  author: 'Von',
  auth: {
    authorized: 'Autorisiert',
    setup: 'Autorisierung einrichten, um zu nutzen',
    setupModalTitle: 'Autorisierung einrichten',
    setupModalTitleDescription: 'Nach der Konfiguration der Anmeldeinformationen können alle Mitglieder im Arbeitsbereich dieses Werkzeug beim Orchestrieren von Anwendungen nutzen.',
  },
  includeToolNum: '{{num}} Werkzeuge inkludiert',
  addTool: 'Werkzeug hinzufügen',
  createTool: {
    title: 'Eigenes Werkzeug erstellen',
    editAction: 'Konfigurieren',
    editTitle: 'Eigenes Werkzeug bearbeiten',
    name: 'Name',
    toolNamePlaceHolder: 'Geben Sie den Werkzeugnamen ein',
    schema: 'Schema',
    schemaPlaceHolder: 'Geben Sie hier Ihr OpenAPI-Schema ein',
    viewSchemaSpec: 'Die OpenAPI-Swagger-Spezifikation anzeigen',
    importFromUrl: 'Von URL importieren',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Bitte geben Sie eine gültige URL ein',
    examples: 'Beispiele',
    exampleOptions: {
      json: 'Wetter(JSON)',
      yaml: 'Pet Store(YAML)',
      blankTemplate: 'Leere Vorlage',
    },
    availableTools: {
      title: 'Verfügbare Werkzeuge',
      name: 'Name',
      description: 'Beschreibung',
      method: 'Methode',
      path: 'Pfad',
      action: 'Aktionen',
      test: 'Test',
    },
    authMethod: {
      title: 'Autorisierungsmethode',
      type: 'Autorisierungstyp',
      keyTooltip: 'Http Header Key, Sie können es bei "Authorization" belassen, wenn Sie nicht wissen, was es ist, oder auf einen benutzerdefinierten Wert setzen',
      types: {
        none: 'Keine',
        api_key: 'API-Key',
        apiKeyPlaceholder: 'HTTP-Headername für API-Key',
        apiValuePlaceholder: 'API-Key eingeben',
        api_key_header: 'Kopfzeile',
        queryParamPlaceholder: 'Abfrageparametername für den API-Schlüssel',
        api_key_query: 'Abfrageparameter',
      },
      key: 'Schlüssel',
      value: 'Wert',
      queryParam: 'Abfrageparameter',
      queryParamTooltip: 'Der Name des API-Schlüssel-Abfrageparameters, der übergeben werden soll, z. B. "key" in "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'Auth-Typ',
      types: {
        basic: 'Basic',
        bearer: 'Bearer',
        custom: 'Benutzerdefiniert',
      },
    },
    privacyPolicy: 'Datenschutzrichtlinie',
    privacyPolicyPlaceholder: 'Bitte Datenschutzrichtlinie eingeben',
    customDisclaimer: 'Benutzer Haftungsausschluss',
    customDisclaimerPlaceholder: 'Bitte benutzerdefinierten Haftungsausschluss eingeben',
    deleteToolConfirmTitle: 'Löschen Sie dieses Werkzeug?',
    deleteToolConfirmContent: 'Das Löschen des Werkzeugs ist irreversibel. Benutzer können Ihr Werkzeug nicht mehr verwenden.',
    toolInput: {
      description: 'Beschreibung',
      methodParameterTip: 'LLM-Füllungen während der Inferenz',
      method: 'Methode',
      methodParameter: 'Parameter',
      label: 'Schilder',
      required: 'Erforderlich',
      methodSetting: 'Einstellung',
      name: 'Name',
      title: 'Werkzeug-Eingabe',
      methodSettingTip: 'Der Benutzer füllt die Werkzeugkonfiguration aus',
      descriptionPlaceholder: 'Beschreibung der Bedeutung des Parameters',
      labelPlaceholder: 'Tags auswählen(optional)',
    },
    description: 'Beschreibung',
    confirmTip: 'Apps, die dieses Tool verwenden, sind davon betroffen',
    nameForToolCallTip: 'Unterstützt nur Zahlen, Buchstaben und Unterstriche.',
    nameForToolCall: 'Name des Werkzeugaufrufs',
    confirmTitle: 'Bestätigen, um zu speichern?',
    nameForToolCallPlaceHolder: 'Wird für die Maschinenerkennung verwendet, z. B. getCurrentWeather, list_pets',
    descriptionPlaceholder: 'Kurze Beschreibung des Zwecks des Werkzeugs, z. B. um die Temperatur für einen bestimmten Ort zu ermitteln.',
  },
  test: {
    title: 'Test',
    parametersValue: 'Parameter & Wert',
    parameters: 'Parameter',
    value: 'Wert',
    testResult: 'Testergebnisse',
    testResultPlaceholder: 'Testergebnis wird hier angezeigt',
  },
  thought: {
    using: 'Nutzung',
    used: 'Genutzt',
    requestTitle: 'Anfrage an',
    responseTitle: 'Antwort von',
  },
  setBuiltInTools: {
    info: 'Info',
    setting: 'Einstellung',
    toolDescription: 'Werkzeugbeschreibung',
    parameters: 'Parameter',
    string: 'Zeichenkette',
    number: 'Nummer',
    required: 'Erforderlich',
    infoAndSetting: 'Info & Einstellungen',
    file: 'Datei',
  },
  noCustomTool: {
    title: 'Keine benutzerdefinierten Werkzeuge!',
    content: 'Fügen Sie hier Ihre benutzerdefinierten Werkzeuge hinzu und verwalten Sie sie, um KI-Apps zu erstellen.',
    createTool: 'Werkzeug erstellen',
  },
  noSearchRes: {
    title: 'Leider keine Ergebnisse!',
    content: 'Wir konnten keine Werkzeuge finden, die Ihrer Suche entsprechen.',
    reset: 'Suche zurücksetzen',
  },
  builtInPromptTitle: 'Aufforderung',
  toolRemoved: 'Werkzeug entfernt',
  notAuthorized: 'Werkzeug nicht autorisiert',
  howToGet: 'Wie erhält man',
  addToolModal: {
    type: 'Art',
    category: 'Kategorie',
    add: 'hinzufügen',
    added: 'zugefügt',
    manageInTools: 'Verwalten in Tools',
    custom: {
      title: 'Kein benutzerdefiniertes Werkzeug verfügbar',
      tip: 'Benutzerdefiniertes Werkzeug erstellen',
    },
    workflow: {
      title: 'Kein Workflow-Werkzeug verfügbar',
      tip: 'Veröffentlichen Sie Workflows als Werkzeuge im Studio',
    },
    mcp: {
      title: 'Kein MCP-Werkzeug verfügbar',
      tip: 'Einen MCP-Server hinzufügen',
    },
    agent: {
      title: 'Keine Agentenstrategie verfügbar',
    },
  },
  toolNameUsageTip: 'Name des Tool-Aufrufs für die Argumentation und Aufforderung des Agenten',
  customToolTip: 'Erfahren Sie mehr über benutzerdefinierte Dify-Tools',
  openInStudio: 'In Studio öffnen',
  noTools: 'Keine Werkzeuge gefunden',
  copyToolName: 'Name kopieren',
  mcp: {
    create: {
      cardTitle: 'MCP-Server hinzufügen (HTTP)',
      cardLink: 'Mehr über MCP-Server-Integration erfahren',
    },
    noConfigured: 'Nicht konfigurierter Server',
    updateTime: 'Aktualisiert',
    toolsCount: '{{count}} Tools',
    noTools: 'Keine Tools verfügbar',
    modal: {
      title: 'MCP-Server hinzufügen (HTTP)',
      editTitle: 'MCP-Server bearbeiten (HTTP)',
      name: 'Name & Symbol',
      namePlaceholder: 'Benennen Sie Ihren MCP-Server',
      serverUrl: 'Server-URL',
      serverUrlPlaceholder: 'URL zum Server-Endpunkt',
      serverUrlWarning: 'Das Ändern der Serveradresse kann Anwendungen unterbrechen, die von diesem Server abhängen',
      serverIdentifier: 'Serverkennung',
      serverIdentifierTip: 'Eindeutige Kennung für den MCP-Server im Arbeitsbereich. Nur Kleinbuchstaben, Zahlen, Unterstriche und Bindestriche. Maximal 24 Zeichen.',
      serverIdentifierPlaceholder: 'Eindeutige Kennung, z.B. mein-mcp-server',
      serverIdentifierWarning: 'Nach einer ID-Änderung wird der Server von vorhandenen Apps nicht erkannt',
      cancel: 'Abbrechen',
      save: 'Speichern',
      confirm: 'Hinzufügen & Autorisieren',
    },
    delete: 'MCP-Server entfernen',
    deleteConfirmTitle: 'Möchten Sie {{mcp}} entfernen?',
    operation: {
      edit: 'Bearbeiten',
      remove: 'Entfernen',
    },
    authorize: 'Autorisieren',
    authorizing: 'Wird autorisiert...',
    authorizingRequired: 'Autorisierung erforderlich',
    authorizeTip: 'Nach der Autorisierung werden Tools hier angezeigt.',
    update: 'Aktualisieren',
    updating: 'Wird aktualisiert',
    gettingTools: 'Tools werden abgerufen...',
    updateTools: 'Tools werden aktualisiert...',
    toolsEmpty: 'Tools nicht geladen',
    getTools: 'Tools abrufen',
    toolUpdateConfirmTitle: 'Tool-Liste aktualisieren',
    toolUpdateConfirmContent: 'Das Aktualisieren der Tool-Liste kann bestehende Apps beeinflussen. Fortfahren?',
    toolsNum: '{{count}} Tools enthalten',
    onlyTool: '1 Tool enthalten',
    identifier: 'Serverkennung (Zum Kopieren klicken)',
    server: {
      title: 'MCP-Server',
      url: 'Server-URL',
      reGen: 'Server-URL neu generieren?',
      addDescription: 'Beschreibung hinzufügen',
      edit: 'Beschreibung bearbeiten',
      modal: {
        addTitle: 'Beschreibung hinzufügen, um MCP-Server zu aktivieren',
        editTitle: 'Beschreibung bearbeiten',
        description: 'Beschreibung',
        descriptionPlaceholder: 'Erklären Sie, was dieses Tool tut und wie es vom LLM verwendet werden soll',
        parameters: 'Parameter',
        parametersTip: 'Fügen Sie Beschreibungen für jeden Parameter hinzu, um dem LLM Zweck und Einschränkungen zu verdeutlichen.',
        parametersPlaceholder: 'Zweck und Einschränkungen des Parameters',
        confirm: 'MCP-Server aktivieren',
      },
      publishTip: 'App nicht veröffentlicht. Bitte zuerst die App veröffentlichen.',
    },
  },
}

export default translation
