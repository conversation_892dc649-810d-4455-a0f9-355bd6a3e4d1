const translation = {
  title: 'یادداشت‌ها',
  name: 'پ<PERSON><PERSON><PERSON> یادداشت',
  editBy: 'پاسخ ویرایش شده توسط {{author}}',
  noData: {
    title: 'بدون یادداشت',
    description: 'شما می‌توانید یادداشت‌ها را در حین اشکال‌زدایی برنامه ویرایش کنید یا یادداشت‌ها را به صورت انبوه در اینجا برای پاسخگویی با کیفیت بالا وارد کنید.',
  },
  table: {
    header: {
      question: 'سوال',
      answer: 'پاسخ',
      createdAt: 'ایجاد شده در',
      hits: 'بازدیدها',
      actions: 'اقدامات',
      addAnnotation: 'افزودن یادداشت',
      bulkImport: 'واردات انبوه',
      bulkExport: 'صادرات انبوه',
      clearAll: 'پاک کردن همه یادداشت‌ها',
    },
  },
  editModal: {
    title: 'ویرایش پاسخ یادداشت',
    queryName: 'پرسش کاربر',
    answerName: 'ربات داستان‌سرا',
    yourAnswer: 'پاسخ شما',
    answerPlaceholder: 'پاسخ خود را اینجا بنویسید',
    yourQuery: 'پرسش شما',
    queryPlaceholder: 'پرسش خود را اینجا بنویسید',
    removeThisCache: 'حذف این یادداشت',
    createdAt: 'ایجاد شده در',
  },
  addModal: {
    title: 'افزودن پاسخ یادداشت',
    queryName: 'سوال',
    answerName: 'پاسخ',
    answerPlaceholder: 'پاسخ را اینجا بنویسید',
    queryPlaceholder: 'پرسش را اینجا بنویسید',
    createNext: 'افزودن پاسخ یادداشت‌شده دیگر',
  },
  batchModal: {
    title: 'واردات انبوه',
    csvUploadTitle: 'فایل CSV خود را اینجا بکشید و رها کنید، یا ',
    browse: 'مرور کنید',
    tip: 'فایل CSV باید از ساختار زیر پیروی کند:',
    question: 'سوال',
    answer: 'پاسخ',
    contentTitle: 'محتوای تکه',
    content: 'محتوا',
    template: 'الگو را از اینجا دانلود کنید',
    cancel: 'لغو',
    run: 'اجرای دسته‌ای',
    runError: 'اجرای دسته‌ای ناموفق بود',
    processing: 'در حال پردازش دسته‌ای',
    completed: 'واردات تکمیل شد',
    error: 'خطای واردات',
    ok: 'تایید',
  },
  errorMessage: {
    answerRequired: 'پاسخ الزامی است',
    queryRequired: 'سوال الزامی است',
  },
  viewModal: {
    annotatedResponse: 'پاسخ یادداشت‌شده',
    hitHistory: 'تاریخچه بازدید',
    hit: 'بازدید',
    hits: 'بازدیدها',
    noHitHistory: 'بدون تاریخچه بازدید',
  },
  hitHistoryTable: {
    query: 'پرسش',
    match: 'تطابق',
    response: 'پاسخ',
    source: 'منبع',
    score: 'امتیاز',
    time: 'زمان',
  },
  initSetup: {
    title: 'راه‌اندازی اولیه پاسخ یادداشت',
    configTitle: 'تنظیمات پاسخ یادداشت',
    confirmBtn: 'ذخیره و فعال‌سازی',
    configConfirmBtn: 'ذخیره',
  },
  embeddingModelSwitchTip: 'مدل برداری‌سازی متن یادداشت، تغییر مدل‌ها باعث جاسازی مجدد خواهد شد و هزینه‌های اضافی به همراه خواهد داشت.',
}

export default translation
