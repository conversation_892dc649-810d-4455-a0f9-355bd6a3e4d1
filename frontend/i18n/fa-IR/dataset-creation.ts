const translation = {
  steps: {
    header: {
      creation: 'ایجاد دانش',
      update: 'افزودن داده',
      fallbackRoute: 'دانش',
    },
    one: 'انتخاب منبع داده',
    two: 'پیشپردازش و پاکسازی متن',
    three: 'اجرا و پایان',
  },
  error: {
    unavailable: 'این دانش در دسترس نیست',
  },
  firecrawl: {
    configFirecrawl: 'پیکربندی fireFirecrawl',
    apiKeyPlaceholder: 'کلید API از firecrawl.dev',
    getApiKeyLinkText: 'کلید API خود را از firecrawl.dev دریافت کنید',
  },
  stepOne: {
    filePreview: 'پیشنمایش فایل',
    pagePreview: 'پیشنمایش صفحه',
    dataSourceType: {
      file: 'وارد کردن از فایل',
      notion: 'همگامسازی از Notion',
      web: 'همگامسازی از وبسایت',
    },
    uploader: {
      title: 'بارگذاری فایل',
      button: 'فایل ها یا پوشه ها را بکشید و رها کنید یا',
      browse: 'مرور',
      tip: 'پشتیبانی از {{supportTypes}}. حداکثر {{size}}MB هر کدام.',
      validation: {
        typeError: 'نوع فایل پشتیبانی نمیشود',
        size: 'فایل خیلی بزرگ است. حداکثر {{size}}MB',
        count: 'چندین فایل پشتیبانی نمیشود',
        filesNumber: 'شما به حد مجاز بارگذاری دستهای {{filesNumber}} رسیدهاید.',
      },
      cancel: 'لغو',
      change: 'تغییر',
      failed: 'بارگذاری ناموفق بود',
    },
    notionSyncTitle: 'Notion متصل نیست',
    notionSyncTip: 'برای همگامسازی با Notion، ابتدا باید اتصال به Notion برقرار شود.',
    connect: 'رفتن به اتصال',
    button: 'بعدی',
    emptyDatasetCreation: 'میخواهم یک دانش خالی ایجاد کنم',
    modal: {
      title: 'ایجاد یک دانش خالی',
      tip: 'یک دانش خالی هیچ سندی نخواهد داشت و شما میتوانید هر زمان اسناد را بارگذاری کنید.',
      input: 'نام دانش',
      placeholder: 'لطفاً وارد کنید',
      nameNotEmpty: 'نام نمیتواند خالی باشد',
      nameLengthInvalid: 'نام باید بین 1 تا 40 کاراکتر باشد',
      cancelButton: 'لغو',
      confirmButton: 'ایجاد',
      failed: 'ایجاد ناموفق بود',
    },
    website: {
      fireCrawlNotConfigured: 'Firecrawl پیکربندی نشده است',
      fireCrawlNotConfiguredDescription: 'برای استفاده از Firecrawl با کلید API پیکربندی کنید.',
      configure: 'پیکربندی',
      run: 'اجرا',
      firecrawlTitle: 'استخراج محتوای وب با fireFirecrawl',
      firecrawlDoc: 'مستندات Firecrawl',
      options: 'گزینهها',
      crawlSubPage: 'خزش صفحات فرعی',
      limit: 'محدودیت',
      maxDepth: 'حداکثر عمق',
      excludePaths: 'مسیرهای مستثنی',
      includeOnlyPaths: 'فقط مسیرهای شامل',
      extractOnlyMainContent: 'فقط محتوای اصلی را استخراج کنید (بدون هدرها، ناوبریها، پاورقیها و غیره)',
      exceptionErrorTitle: 'یک استثنا در حین اجرای کار Firecrawl رخ داد:',
      unknownError: 'خطای ناشناخته',
      totalPageScraped: 'کل صفحات خراشیده شده:',
      selectAll: 'انتخاب همه',
      resetAll: 'بازنشانی همه',
      scrapTimeInfo: 'در مجموع {{total}} صفحه در {{time}} ثانیه خراشیده شد',
      preview: 'پیشنمایش',
      maxDepthTooltip: 'حداکثر عمق برای خزش نسبت به URL وارد شده. عمق 0 فقط صفحه URL وارد شده را خراش میدهد، عمق 1 URL و همه چیز بعد از URL وارد شده + یک / را خراش میدهد، و غیره.',
      jinaReaderDocLink: 'https://jina.ai/reader',
      chooseProvider: 'یک ارائه دهنده را انتخاب کنید',
      jinaReaderTitle: 'کل سایت را به Markdown تبدیل کنید',
      jinaReaderNotConfigured: 'Jina Reader پیکربندی نشده است',
      jinaReaderDoc: 'درباره Jina Reader بیشتر بدانید',
      useSitemap: 'از نقشه سایت استفاده کنید',
      jinaReaderNotConfiguredDescription: 'با وارد کردن کلید API رایگان خود برای دسترسی، Jina Reader را راه اندازی کنید.',
      useSitemapTooltip: 'نقشه سایت را دنبال کنید تا سایت را بخزید. در غیر این صورت، Jina Reader بر اساس ارتباط صفحه به صورت تکراری می خزد و صفحات کمتر اما با کیفیت بالاتر را به دست می آورد.',
      watercrawlDoc: 'مستندات واتر کراول',
      configureFirecrawl: 'تنظیم Firecrawl',
      waterCrawlNotConfiguredDescription: 'برای استفاده از Watercrawl، آن را با کلید API پیکربندی کنید.',
      waterCrawlNotConfigured: 'Watercrawl پیکربندی نشده است',
      configureJinaReader: 'پیکربندی خواننده جینا',
      watercrawlTitle: 'محتوای وب را با واترکرال استخراج کنید',
      configureWatercrawl: 'تنظیم واترکراول',
    },
    cancel: 'لغو',
  },
  stepTwo: {
    segmentation: 'تنظیمات بخشبندی',
    auto: 'خودکار',
    autoDescription: 'به طور خودکار قوانین بخشبندی و پیشپردازش را تنظیم کنید. به کاربران ناآشنا توصیه میشود این گزینه را انتخاب کنند.',
    custom: 'سفارشی',
    customDescription: 'قوانین بخشبندی، طول بخشها و قوانین پیشپردازش را سفارشی کنید، و غیره.',
    separator: 'شناسه بخش',
    separatorPlaceholder: 'برای مثال، خط جدید (\\\\n) یا جداکننده خاص (مانند "***")',
    maxLength: 'حداکثر طول بخش',
    overlap: 'همپوشانی بخش',
    overlapTip: 'تنظیم همپوشانی بخش میتواند ارتباط معنایی بین آنها را حفظ کند و اثر بازیابی را افزایش دهد. توصیه میشود 10%-25% از حداکثر اندازه بخش تنظیم شود.',
    overlapCheck: 'همپوشانی بخش نباید بزرگتر از طول حداکثر بخش باشد',
    rules: 'قوانین پیشپردازش متن',
    removeExtraSpaces: 'جایگزینی فضاهای متوالی، خطوط جدید و تبها',
    removeUrlEmails: 'حذف همه URLها و آدرسهای ایمیل',
    removeStopwords: 'حذف کلمات توقف مانند "a"، "an"، "the"',
    preview: 'تأیید و پیشنمایش',
    reset: 'بازنشانی',
    indexMode: 'حالت شاخص',
    qualified: 'کیفیت بالا',
    recommend: 'توصیه شده',
    qualifiedTip: 'رابط جاسازی سیستم پیشفرض را برای پردازش فراخوانی کنید تا دقت بالاتری هنگام پرسش کاربران فراهم شود.',
    warning: 'لطفاً ابتدا کلید API ارائهدهنده مدل را تنظیم کنید.',
    click: 'رفتن به تنظیمات',
    economical: 'اقتصادی',
    economicalTip: 'از موتورهای برداری آفلاین، شاخصهای کلیدواژه و غیره استفاده کنید تا دقت را بدون صرف توکنها کاهش دهید',
    QATitle: 'بخشبندی در قالب پرسش و پاسخ',
    QATip: 'فعال کردن این گزینه توکنهای بیشتری مصرف خواهد کرد',
    QALanguage: 'بخشبندی با استفاده از',
    estimateCost: 'برآورد',
    estimateSegment: 'بخشهای برآورد شده',
    segmentCount: 'بخشها',
    calculating: 'در حال محاسبه...',
    fileSource: 'پیشپردازش اسناد',
    notionSource: 'پیشپردازش صفحات',
    websiteSource: 'پیشپردازش وبسایت',
    other: 'و سایر',
    fileUnit: ' فایلها',
    notionUnit: ' صفحات',
    webpageUnit: ' صفحات',
    previousStep: 'مرحله قبلی',
    nextStep: 'ذخیره و پردازش',
    save: 'ذخیره و پردازش',
    cancel: 'لغو',
    sideTipTitle: 'چرا بخشبندی و پیشپردازش؟',
    sideTipP1: 'هنگام پردازش دادههای متنی، بخشبندی و پاکسازی دو مرحله مهم پیشپردازش هستند.',
    sideTipP2: 'بخشبندی متن طولانی را به پاراگرافها تقسیم میکند تا مدلها بهتر بتوانند آن را درک کنند. این کیفیت و ارتباط نتایج مدل را بهبود میبخشد.',
    sideTipP3: 'پاکسازی کاراکترها و فرمتهای غیرضروری را حذف میکند و دانش را پاکتر و آسانتر برای تجزیه میکند.',
    sideTipP4: 'بخشبندی و پاکسازی مناسب عملکرد مدل را بهبود میبخشد و نتایج دقیقتر و ارزشمندتری ارائه میدهد.',
    previewTitle: 'پیشنمایش',
    previewTitleButton: 'پیشنمایش',
    previewButton: 'تغییر به قالب پرسش و پاسخ',
    previewSwitchTipStart: 'پیشنمایش بخش فعلی در قالب متن است، تغییر به پیشنمایش قالب پرسش و پاسخ',
    previewSwitchTipEnd: ' توکنهای اضافی مصرف خواهد کرد',
    characters: 'کاراکترها',
    indexSettingTip: 'برای تغییر روش شاخص، لطفاً به',
    retrievalSettingTip: 'برای تغییر روش شاخص، لطفاً به',
    datasetSettingLink: 'تنظیمات دانش بروید.',
    separatorTip: 'جداکننده نویسه ای است که برای جداسازی متن استفاده می شود. \\n\\n و \\n معمولا برای جداسازی پاراگراف ها و خطوط استفاده می شوند. همراه با کاما (\\n\\n,\\n)، پاراگراف ها زمانی که از حداکثر طول تکه فراتر می روند، با خطوط تقسیم بندی می شوند. همچنین می توانید از جداکننده های خاصی که توسط خودتان تعریف شده اند استفاده کنید (مثلا ***).',
    maxLengthCheck: 'حداکثر طول تکه باید کمتر از {{limit}} باشد',
    notAvailableForQA: 'برای شاخص پرسش و پاسخ در دسترس نیست',
    parentChild: 'پدر و مادر و فرزند',
    qaSwitchHighQualityTipContent: 'در حال حاضر، فقط روش شاخص با کیفیت بالا از تکه تکه کردن فرمت پرسش و پاسخ پشتیبانی می کند. آیا می خواهید به حالت با کیفیت بالا بروید؟',
    previewChunk: 'پیش نمایش تکه',
    previewChunkCount: '{{تعداد}} تکه های تخمینی',
    previewChunkTip: 'روی دکمه "پیش نمایش قطعه" در سمت چپ کلیک کنید تا پیش نمایش بارگیری شود',
    general: 'عمومی',
    paragraphTip: 'این حالت متن را بر اساس جداکننده ها و حداکثر طول تکه به پاراگراف ها تقسیم می کند و از متن تقسیم شده به عنوان تکه والد برای بازیابی استفاده می کند.',
    parentChunkForContext: 'تکه والد برای زمینه',
    fullDoc: 'مستند کامل',
    switch: 'سوئیچ',
    parentChildChunkDelimiterTip: 'جداکننده نویسه ای است که برای جداسازی متن استفاده می شود. \\n برای تقسیم تکه های والد به تکه های کوچک کودک توصیه می شود. همچنین می توانید از جداکننده های ویژه ای که توسط خودتان تعریف شده است استفاده کنید.',
    generalTip: 'حالت تکه تکه کردن متن عمومی، تکه های بازیابی شده و فراخوانی شده یکسان هستند.',
    paragraph: 'پاراگراف',
    highQualityTip: 'پس از اتمام جاسازی در حالت کیفیت بالا، بازگشت به حالت اقتصادی در دسترس نیست.',
    parentChildTip: 'هنگام استفاده از حالت والد-فرزند، تکه فرزند برای بازیابی و تکه والد برای یادآوری به عنوان زمینه استفاده می شود.',
    notAvailableForParentChild: 'برای نمایه والد-فرزند در دسترس نیست',
    parentChildDelimiterTip: 'جداکننده نویسه ای است که برای جداسازی متن استفاده می شود. \\n\\n برای تقسیم سند اصلی به تکه های والد بزرگ توصیه می شود. همچنین می توانید از جداکننده های ویژه ای که توسط خودتان تعریف شده است استفاده کنید.',
    childChunkForRetrieval: 'تکه کودک برای بازیابی',
    fullDocTip: 'کل سند به عنوان تکه والد استفاده می شود و مستقیما بازیابی می شود. لطفا توجه داشته باشید که به دلایل عملکردی، متن بیش از 10000 توکن به طور خودکار کوتاه می شود.',
    qaSwitchHighQualityTipTitle: 'فرمت پرسش و پاسخ به روش نمایه سازی با کیفیت بالا نیاز دارد',
    useQALanguage: 'تکه با استفاده از فرمت پرسش و پاسخ در',
  },
  stepThree: {
    creationTitle: ' دانش ایجاد شد',
    creationContent: 'ما به طور خودکار نام دانش را تعیین کردیم، شما میتوانید هر زمان آن را تغییر دهید',
    label: 'نام دانش',
    additionTitle: ' سند بارگذاری شد',
    additionP1: 'سند به دانش بارگذاری شده است',
    additionP2: '، میتوانید آن را در لیست اسناد دانش پیدا کنید.',
    stop: 'توقف پردازش',
    resume: 'ادامه پردازش',
    navTo: 'رفتن به سند',
    sideTipTitle: 'بعدی چیست',
    sideTipContent: 'پس از اتمام فهرستبندی سند، دانش میتواند به عنوان زمینه در برنامه یکپارچه شود، میتوانید تنظیمات زمینه را در صفحه ارکستراسیون درخواست پیدا کنید. همچنین میتوانید آن را به عنوان یک افزونه فهرستبندی مستقل ChatGPT برای انتشار ایجاد کنید.',
    modelTitle: 'آیا مطمئن هستید که میخواهید جاسازی را متوقف کنید؟',
    modelContent: 'اگر نیاز به ادامه پردازش بعداً دارید، از جایی که متوقف شدهاید ادامه خواهید داد.',
    modelButtonConfirm: 'تأیید',
    modelButtonCancel: 'لغو',
  },
  jinaReader: {
    configJinaReader: 'پیکربندی Jina Reader',
    apiKeyPlaceholder: 'کلید API از jina.ai',
    getApiKeyLinkText: 'کلید API رایگان خود را در jina.ai دریافت کنید',
  },
  otherDataSource: {
    learnMore: 'بیشتر بدانید',
    description: 'در حال حاضر، پایگاه دانش Dify فقط منابع داده محدودی دارد. کمک به یک منبع داده به پایگاه دانش Dify راهی فوق العاده برای کمک به افزایش انعطاف پذیری و قدرت پلتفرم برای همه کاربران است. راهنمای مشارکت ما شروع کار را آسان می کند. لطفا برای کسب اطلاعات بیشتر روی لینک زیر کلیک کنید.',
    title: 'به منابع داده دیگر متصل شوید؟',
  },
  watercrawl: {
    getApiKeyLinkText: 'کلید API خود را از watercrawl.dev دریافت کنید',
    configWatercrawl: 'تنظیم واترکراول',
    apiKeyPlaceholder: 'کلید API از watercrawl.dev',
  },
}

export default translation
