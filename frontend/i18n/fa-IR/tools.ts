const translation = {
  title: 'ابزارها',
  createCustomTool: 'ایجاد ابزار سفارشی',
  customToolTip: 'بیشتر در مورد ابزارهای سفارشی Dify بیاموزید',
  type: {
    all: 'همه',
    builtIn: 'سفارشی شده',
    custom: 'سفارشی',
    workflow: 'جریان کار',
  },
  contribute: {
    line1: 'من علاقه‌مند به ',
    line2: 'مشارکت در ابزارهای Dify هستم.',
    viewGuide: 'مشاهده راهنما',
  },
  author: 'توسط',
  auth: {
    authorized: 'مجوز داده شده',
    setup: 'تنظیم مجوز برای استفاده',
    setupModalTitle: 'تنظیم مجوز',
    setupModalTitleDescription: 'پس از پیکربندی اعتبارنامه‌ها، همه اعضای موجود در فضای کاری می‌توانند از این ابزار هنگام هماهنگی برنامه‌ها استفاده کنند.',
  },
  includeToolNum: '{{num}} ابزار شامل شد',
  addTool: 'افزودن ابزار',
  addToolModal: {
    type: 'نوع',
    category: 'دسته‌بندی',
    add: 'افزودن',
    added: 'افزوده شد',
    manageInTools: 'مدیریت در ابزارها',
    custom: {
      title: 'هیچ ابزار سفارشی موجود نیست',
      tip: 'یک ابزار سفارشی ایجاد کنید',
    },
    workflow: {
      title: 'هیچ ابزار جریان کاری موجود نیست',
      tip: 'جریان‌های کاری را به عنوان ابزار در استودیو منتشر کنید',
    },
    mcp: {
      title: 'هیچ ابزار MCP موجود نیست',
      tip: 'یک سرور MCP اضافه کنید',
    },
    agent: {
      title: 'هیچ استراتژی عاملی موجود نیست',
    },
  },
  createTool: {
    title: 'ایجاد ابزار سفارشی',
    editAction: 'پیکربندی',
    editTitle: 'ویرایش ابزار سفارشی',
    name: 'نام',
    toolNamePlaceHolder: 'نام ابزار را وارد کنید',
    nameForToolCall: 'نام فراخوانی ابزار',
    nameForToolCallPlaceHolder: 'برای شناسایی ماشین، مانند getCurrentWeather، list_pets',
    nameForToolCallTip: 'فقط اعداد، حروف و خط زیر پشتیبانی می‌شود.',
    description: 'توضیحات',
    descriptionPlaceholder: 'توضیحات مختصر در مورد هدف ابزار، مثلاً، گرفتن دما برای یک مکان خاص.',
    schema: 'طرح',
    schemaPlaceHolder: 'طرح OpenAPI خود را اینجا وارد کنید',
    viewSchemaSpec: 'مشاهده مشخصات OpenAPI-Swagger',
    importFromUrl: 'وارد کردن از URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'لطفاً یک URL معتبر وارد کنید',
    examples: 'مثال‌ها',
    exampleOptions: {
      json: 'آب و هوا (JSON)',
      yaml: 'فروشگاه حیوانات خانگی (YAML)',
      blankTemplate: 'الگوی خالی',
    },
    availableTools: {
      title: 'ابزارهای موجود',
      name: 'نام',
      description: 'توضیحات',
      method: 'روش',
      path: 'مسیر',
      action: 'عملیات',
      test: 'آزمایش',
    },
    authMethod: {
      title: 'روش مجوز',
      type: 'نوع مجوز',
      keyTooltip: 'کلید Http Header، می‌توانید آن را با "Authorization" ترک کنید اگر نمی‌دانید چیست یا آن را به یک مقدار سفارشی تنظیم کنید',
      types: {
        none: 'هیچ',
        api_key: 'کلید API',
        apiKeyPlaceholder: 'نام هدر HTTP برای کلید API',
        apiValuePlaceholder: 'کلید API را وارد کنید',
        api_key_header: 'عنوان',
        api_key_query: 'پارامتر جستجو',
        queryParamPlaceholder: 'نام پارامتر جستجو برای کلید API',
      },
      key: 'کلید',
      value: 'مقدار',
      queryParam: 'پارامتر جستجو',
      queryParamTooltip: 'نام پارامتر پرس و جو کلید API که باید ارسال شود، به عنوان مثال "key" در "https://example.com/test?key=API_KEY".',
    },
    authHeaderPrefix: {
      title: 'نوع مجوز',
      types: {
        basic: 'پایه',
        bearer: 'Bearer',
        custom: 'سفارشی',
      },
    },
    privacyPolicy: 'سیاست حفظ حریم خصوصی',
    privacyPolicyPlaceholder: 'لطفاً سیاست حفظ حریم خصوصی را وارد کنید',
    toolInput: {
      title: 'ورودی ابزار',
      name: 'نام',
      required: 'الزامی',
      method: 'روش',
      methodSetting: 'تنظیم',
      methodSettingTip: 'کاربر پیکربندی ابزار را پر می‌کند',
      methodParameter: 'پارامتر',
      methodParameterTip: 'LLM در طول استنباط پر می‌کند',
      label: 'برچسب‌ها',
      labelPlaceholder: 'برچسب‌ها را انتخاب کنید (اختیاری)',
      description: 'توضیحات',
      descriptionPlaceholder: 'توضیحات معنی پارامتر',
    },
    customDisclaimer: 'توجهیه سفارشی',
    customDisclaimerPlaceholder: 'لطفاً توجهیه سفارشی را وارد کنید',
    confirmTitle: 'آیا می‌خواهید ذخیره کنید؟',
    confirmTip: 'برنامه‌هایی که از این ابزار استفاده می‌کنند تحت تأثیر قرار خواهند گرفت',
    deleteToolConfirmTitle: 'آیا این ابزار را حذف کنید؟',
    deleteToolConfirmContent: 'حذف ابزار غیرقابل بازگشت است. کاربران دیگر قادر به دسترسی به ابزار شما نخواهند بود.',
  },
  test: {
    title: 'آزمایش',
    parametersValue: 'پارامترها و مقدار',
    parameters: 'پارامترها',
    value: 'مقدار',
    testResult: 'نتایج آزمایش',
    testResultPlaceholder: 'نتیجه آزمایش در اینجا نمایش داده می‌شود',
  },
  thought: {
    using: 'در حال استفاده',
    used: 'استفاده شده',
    requestTitle: 'درخواست به',
    responseTitle: 'پاسخ از',
  },
  setBuiltInTools: {
    info: 'اطلاعات',
    setting: 'تنظیمات',
    toolDescription: 'توضیحات ابزار',
    parameters: 'پارامترها',
    string: 'رشته',
    number: 'عدد',
    required: 'الزامی',
    infoAndSetting: 'اطلاعات و تنظیمات',
    file: 'فایل',
  },
  noCustomTool: {
    title: 'ابزار سفارشی وجود ندارد!',
    content: 'ابزارهای سفارشی خود را در اینجا اضافه و مدیریت کنید تا برنامه‌های هوش مصنوعی بسازید.',
    createTool: 'ایجاد ابزار',
  },
  noSearchRes: {
    title: 'متأسفیم، نتیجه‌ای پیدا نشد!',
    content: 'ما نتوانستیم ابزارهایی که با جستجوی شما مطابقت داشته باشد پیدا کنیم.',
    reset: 'بازنشانی جستجو',
  },
  builtInPromptTitle: 'پرامپت',
  toolRemoved: 'ابزار حذف شد',
  notAuthorized: 'ابزار مجوز ندارد',
  howToGet: 'چگونه دریافت کنید',
  openInStudio: 'باز کردن در استودیو',
  toolNameUsageTip: 'نام فراخوانی ابزار برای استدلال و پرامپت‌های عامل',
  copyToolName: 'کپی نام',
  noTools: 'هیچ ابزاری یافت نشد',
  mcp: {
    create: {
      cardTitle: 'افزودن سرور MCP (HTTP)',
      cardLink: 'اطلاعات بیشتر درباره یکپارچه‌سازی سرور MCP',
    },
    noConfigured: 'سرور پیکربندی نشده',
    updateTime: 'آخرین بروزرسانی',
    toolsCount: '{count} ابزار',
    noTools: 'ابزاری موجود نیست',
    modal: {
      title: 'افزودن سرور MCP (HTTP)',
      editTitle: 'ویرایش سرور MCP (HTTP)',
      name: 'نام و آیکون',
      namePlaceholder: 'برای سرور MCP خود نام انتخاب کنید',
      serverUrl: 'آدرس سرور',
      serverUrlPlaceholder: 'URL نقطه پایانی سرور',
      serverUrlWarning: 'به‌روزرسانی آدرس سرور ممکن است برنامه‌های وابسته به آن را مختل کند',
      serverIdentifier: 'شناسه سرور',
      serverIdentifierTip: 'شناسه منحصر به فرد برای سرور MCP در فضای کاری. فقط حروف کوچک، اعداد، زیرخط و خط تیره. حداکثر 24 کاراکتر.',
      serverIdentifierPlaceholder: 'شناسه منحصر به فرد، مثال: my-mcp-server',
      serverIdentifierWarning: 'پس از تغییر شناسه، سرور توسط برنامه‌های موجود شناسایی نخواهد شد',
      cancel: 'لغو',
      save: 'ذخیره',
      confirm: 'افزودن و مجوزدهی',
    },
    delete: 'حذف سرور MCP',
    deleteConfirmTitle: 'آیا مایل به حذف {mcp} هستید؟',
    operation: {
      edit: 'ویرایش',
      remove: 'حذف',
    },
    authorize: 'مجوزدهی',
    authorizing: 'در حال مجوزدهی...',
    authorizingRequired: 'مجوز مورد نیاز است',
    authorizeTip: 'پس از مجوزدهی، ابزارها در اینجا نمایش داده می‌شوند.',
    update: 'به‌روزرسانی',
    updating: 'در حال به‌روزرسانی...',
    gettingTools: 'دریافت ابزارها...',
    updateTools: 'به‌روزرسانی ابزارها...',
    toolsEmpty: 'ابزارها بارگیری نشدند',
    getTools: 'دریافت ابزارها',
    toolUpdateConfirmTitle: 'به‌روزرسانی فهرست ابزارها',
    toolUpdateConfirmContent: 'به‌روزرسانی فهرست ابزارها ممکن است بر برنامه‌های موجود تأثیر بگذارد. آیا ادامه می‌دهید؟',
    toolsNum: '{count} ابزار موجود است',
    onlyTool: '1 ابزار موجود است',
    identifier: 'شناسه سرور (کلیک برای کپی)',
    server: {
      title: 'سرور MCP',
      url: 'آدرس سرور',
      reGen: 'تولید مجدد آدرس سرور؟',
      addDescription: 'افزودن توضیحات',
      edit: 'ویرایش توضیحات',
      modal: {
        addTitle: 'برای فعال‌سازی سرور MCP توضیحات اضافه کنید',
        editTitle: 'ویرایش توضیحات',
        description: 'توضیحات',
        descriptionPlaceholder: 'عملکرد این ابزار و نحوه استفاده LLM از آن را توضیح دهید',
        parameters: 'پارامترها',
        parametersTip: 'برای کمک به LLM در درک هدف و محدودیت‌ها، برای هر پارامتر توضیحات اضافه کنید.',
        parametersPlaceholder: 'هدف و محدودیت‌های پارامتر',
        confirm: 'فعال‌سازی سرور MCP',
      },
      publishTip: 'برنامه منتشر نشده است. لطفاً ابتدا برنامه را منتشر کنید.',
    },
  },
}

export default translation
