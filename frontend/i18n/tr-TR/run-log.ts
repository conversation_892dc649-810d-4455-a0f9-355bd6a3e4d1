const translation = {
  input: 'GİRİŞ',
  result: 'SONUÇ',
  detail: 'DETAY',
  tracing: 'İZLEME',
  resultPanel: {
    status: 'DURUM',
    time: 'GEÇEN ZAMAN',
    tokens: 'TOPLAM TOKEN',
  },
  meta: {
    title: 'METADATA',
    status: 'Durum',
    version: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    executor: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    startTime: 'Başlam<PERSON> Zamanı',
    time: 'Geçen Zaman',
    tokens: 'Toplam Token',
    steps: 'Çalıştırma Adımları',
  },
  resultEmpty: {
    title: 'Bu çalıştırma sadece JSON formatında çıktı verdi,',
    tipLeft: 'lütfen ',
    link: 'detay paneli',
    tipRight: 'ne gidin ve görüntüleyin.',
  },
  actionLogs: '<PERSON><PERSON><PERSON>ünlü<PERSON>',
  circularInvocationTip: 'Geçerli iş akışında araçların/dü<PERSON><PERSON><PERSON>in döngüsel olarak çağrılması vardır.',
}

export default translation
