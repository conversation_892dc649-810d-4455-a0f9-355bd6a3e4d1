const translation = {
  currentPlan: 'Mevcut Plan',
  upgradeBtn: {
    plain: 'Planı Yükselt',
    encourage: '<PERSON><PERSON><PERSON> Yükselt',
    encourageShort: '<PERSON>ü<PERSON><PERSON><PERSON>',
  },
  viewBilling: 'Faturalandırma ve abonelikleri yönet',
  buyPermissionDeniedTip: 'Abone olmak için lütfen işletme yöneticinize başvurun',
  plansCommon: {
    title: 'Size uygun bir plan seçin',
    yearlyTip: 'Yıllık abonelikle 2 ay ücretsiz!',
    mostPopular: 'En Popüler',
    planRange: {
      monthly: 'Aylık',
      yearly: 'Yıllık',
    },
    month: 'ay',
    year: 'yıl',
    save: 'Tasarruf et ',
    free: 'Ücretsiz',
    currentPlan: 'Mevcut Plan',
    contractSales: 'Satışla iletişime geçin',
    contractOwner: 'Takım yöneticisine başvurun',
    startForFree: 'Ücretsiz Başla',
    getStartedWith: 'ile baş<PERSON>n',
    contactSales: 'Satışlarla İletişime Geçin',
    talkToSales: 'Satışlarla Konuşun',
    modelProviders: 'Model Sağlayıcılar',
    teamMembers: 'Takım Üyeleri',
    annotationQuota: 'Ek Açıklama Kotası',
    buildApps: 'Uygulamalar Oluştur',
    vectorSpace: 'Vektör Alanı',
    vectorSpaceBillingTooltip: 'Her 1MB yaklaşık 1.2 milyon karakter vektörize veri depolayabilir (OpenAI Embeddings ile tahmin edilmiştir, modellere göre farklılık gösterebilir).',
    vectorSpaceTooltip: 'Vektör Alanı, LLM\'lerin verilerinizi anlaması için gerekli uzun süreli hafıza sistemidir.',
    documentsUploadQuota: 'Doküman Yükleme Kotası',
    documentProcessingPriority: 'Doküman İşleme Önceliği',
    documentProcessingPriorityTip: 'Daha yüksek doküman işleme önceliği için planınızı yükseltin.',
    documentProcessingPriorityUpgrade: 'Daha fazla veriyi daha yüksek doğrulukla ve daha hızlı işleyin.',
    priority: {
      'standard': 'Standart',
      'priority': 'Öncelikli',
      'top-priority': 'En Öncelikli',
    },
    logsHistory: 'Günlük Geçmişi',
    customTools: 'Özel Araçlar',
    unavailable: 'Mevcut Değil',
    days: 'gün',
    unlimited: 'Sınırsız',
    support: 'Destek',
    supportItems: {
      communityForums: 'Topluluk forumları',
      emailSupport: 'E-posta desteği',
      priorityEmail: 'Öncelikli e-posta ve sohbet desteği',
      logoChange: 'Logo değişikliği',
      SSOAuthentication: 'SSO kimlik doğrulama',
      personalizedSupport: 'Kişiselleştirilmiş destek',
      dedicatedAPISupport: 'Özel API desteği',
      customIntegration: 'Özel entegrasyon ve destek',
      ragAPIRequest: 'RAG API Talepleri',
      bulkUpload: 'Toplu doküman yükleme',
      agentMode: 'Agent Modu',
      workflow: 'Workflow',
      llmLoadingBalancing: 'LLM Yük Dengeleme',
      llmLoadingBalancingTooltip: 'Modellere birden fazla API anahtarı ekleyin, API hız sınırlarını etkili bir şekilde aşın.',
    },
    comingSoon: 'Yakında geliyor',
    member: 'Üye',
    memberAfter: 'Üye',
    messageRequest: {
      title: 'Mesaj Kredileri',
      tooltip: 'OpenAI modellerini (gpt4 hariç) kullanarak çeşitli planlar için mesaj çağrı kotaları. Limitin üzerindeki mesajlar OpenAI API Anahtarınızı kullanır.',
      titlePerMonth: '{{count,number}} mesaj/ay',
    },
    annotatedResponse: {
      title: 'Ek Açıklama Kota Sınırları',
      tooltip: 'Yanıtların elle düzenlenmesi ve ek açıklanması, uygulamalar için özelleştirilebilir yüksek kaliteli soru-cevap yetenekleri sağlar. (Sadece sohbet uygulamalarında geçerlidir)',
    },
    ragAPIRequestTooltip: 'Dify\'nin sadece bilgi tabanı işleme yeteneklerini çağıran API çağrıları sayısını ifade eder.',
    receiptInfo: 'Sadece takım sahibi ve takım yöneticisi abone olabilir ve faturalandırma bilgilerini görüntüleyebilir',
    documentsTooltip: 'Bilgi Veri Kaynağından ithal edilen belge sayısına kota.',
    freeTrialTipSuffix: 'Kredi kartı gerekmez',
    freeTrialTipPrefix: 'Kaydolun ve bir',
    priceTip: 'iş alanı başına/',
    documentsRequestQuota: '{{count,number}}/dakika Bilgi İsteği Oran Limiti',
    apiRateLimitUnit: '{{count,number}}/gün',
    documents: '{{count,number}} Bilgi Belgesi',
    comparePlanAndFeatures: 'Planları ve özellikleri karşılaştır',
    self: 'Kendi Barındırılan',
    getStarted: 'Başlayın',
    annualBilling: 'Yıllık Faturalama',
    teamMember_one: '{{count,number}} Takım Üyesi',
    apiRateLimit: 'API Hız Limiti',
    cloud: 'Bulut Hizmeti',
    teamMember_other: '{{count,number}} Takım Üyesi',
    apiRateLimitTooltip: 'Dify API\'si aracılığıyla yapılan tüm isteklerde, metin oluşturma, sohbet konuşmaları, iş akışı yürütmeleri ve belge işleme dahil olmak üzere, API Oran Sınırı uygulanır.',
    unlimitedApiRate: 'API Hız Sınırı Yok',
    freeTrialTip: '200 OpenAI çağrısının ücretsiz denemesi.',
    teamWorkspace: '{{count,number}} Takım Çalışma Alanı',
    documentsRequestQuotaTooltip: 'Bir çalışma alanının bilgi tabanında, veri seti oluşturma, silme, güncellemeler, belge yüklemeleri, değişiklikler, arşivleme ve bilgi tabanı sorguları dahil olmak üzere, dakikada gerçekleştirebileceği toplam işlem sayısını belirtir. Bu ölçüt, bilgi tabanı taleplerinin performansını değerlendirmek için kullanılır. Örneğin, bir Sandbox kullanıcısı bir dakika içinde ardışık 10 vurma testi gerçekleştirirse, çalışma alanı bir sonraki dakika için aşağıdaki işlemleri gerçekleştirmesi geçici olarak kısıtlanacaktır: veri seti oluşturma, silme, güncellemeler ve belge yüklemeleri veya değişiklikler.',
  },
  plans: {
    sandbox: {
      name: 'Sandbox',
      description: '200 kez GPT ücretsiz deneme',
      includesTitle: 'İçerdikleri:',
      for: 'Temel Yeteneklerin Ücretsiz Denemesi',
    },
    professional: {
      name: 'Profesyonel',
      description: 'Bireyler ve küçük takımlar için daha fazla güç açın.',
      includesTitle: 'Ücretsiz plandaki her şey, artı:',
      for: 'Bağımsız Geliştiriciler/Küçük Takımlar için',
    },
    team: {
      name: 'Takım',
      description: 'Sınırsız işbirliği ve en üst düzey performans.',
      includesTitle: 'Profesyonel plandaki her şey, artı:',
      for: 'Orta Boyutlu Takımlar İçin',
    },
    enterprise: {
      name: 'Kurumsal',
      description: 'Büyük ölçekli kritik sistemler için tam yetenekler ve destek.',
      includesTitle: 'Takım plandaki her şey, artı:',
      features: {
        3: 'Birden Fazla Çalışma Alanı ve Kurumsal Yönetim',
        8: 'Profesyonel Teknik Destek',
        4: 'SSO',
        2: 'Özel Şirket Özellikleri',
        1: 'Ticari Lisans Yetkilendirmesi',
        7: 'Dify Tarafından Resmi Güncellemeler ve Bakım',
        5: 'Dify Ortakları tarafından müzakere edilen SLA\'lar',
        6: 'Gelişmiş Güvenlik ve Kontroller',
        0: 'Kurumsal Düzeyde Ölçeklenebilir Dağıtım Çözümleri',
      },
      priceTip: 'Yıllık Faturalama Sadece',
      for: 'Büyük boyutlu Takımlar için',
      btnText: 'Satış ile İletişime Geç',
      price: 'Özel',
    },
    community: {
      features: {
        1: 'Tek İş Alanı',
        0: 'Tüm Temel Özellikler Kamu Deposu Altında Yayınlandı',
        2: 'Dify Açık Kaynak Lisansına uyar',
      },
      price: 'Ücretsiz',
      includesTitle: 'Ücretsiz Özellikler:',
      name: 'Topluluk',
      btnText: 'Topluluğa Başlayın',
      for: 'Bireysel Kullanıcılar, Küçük Ekipler veya Ticari Olmayan Projeler İçin',
      description: 'Bireysel Kullanıcılar, Küçük Ekipler veya Ticari Olmayan Projeler İçin',
    },
    premium: {
      features: {
        1: 'Tek İş Alanı',
        0: 'Çeşitli Bulut Sağlayıcıları Tarafından Kendiliğinden Yönetilen Güvenilirlik',
        3: 'Öncelikli Email ve Sohbet Desteği',
        2: 'Web Uygulaması Logo ve Markalaşma Özelleştirmesi',
      },
      name: 'Premium',
      includesTitle: 'Topluluktan her şey, artı:',
      for: 'Orta Büyüklükteki Organizasyonlar ve Ekipler için',
      price: 'Ölçeklenebilir',
      btnText: 'Premium alın',
      priceTip: 'Bulut Pazarına Dayalı',
      description: 'Orta Büyüklükteki Organizasyonlar ve Ekipler için',
      comingSoon: 'Microsoft Azure ve Google Cloud Desteği Yakında Geliyor',
    },
  },
  vectorSpace: {
    fullTip: 'Vektör Alanı dolu.',
    fullSolution: 'Daha fazla alan için planınızı yükseltin.',
  },
  apps: {
    fullTipLine1: 'Daha fazla uygulama oluşturmak için',
    fullTipLine2: 'planınızı yükseltin.',
    contactUs: 'Bizimle iletişime geçin',
    fullTip2des: 'Kullanımı serbest bırakmak için etkisiz uygulamaların temizlenmesi önerilir veya bizimle iletişime geçin.',
    fullTip1des: 'Bu planda uygulama oluşturma limitine ulaştınız.',
    fullTip2: 'Plan limiti aşıldı',
    fullTip1: 'Daha fazla uygulama oluşturmak için yükseltin',
  },
  annotatedResponse: {
    fullTipLine1: 'Daha fazla konuşmayı açıklamak için',
    fullTipLine2: 'planınızı yükseltin.',
    quotaTitle: 'Ek Açıklama Yanıtı Kotası',
  },
  usagePage: {
    teamMembers: 'Ekip Üyeleri',
    vectorSpaceTooltip: 'Yüksek Kalite indeksleme moduna sahip belgeler, Bilgi Veri Depolama kaynaklarını tüketir. Bilgi Veri Depolama sınırına ulaştığında, yeni belgeler yüklenmeyecek.',
    vectorSpace: 'Bilgi Veri Depolama',
    buildApps: 'Uygulama Geliştir',
    annotationQuota: 'Notlandırma Kotası',
    documentsUploadQuota: 'Belgeler Yükleme Kotası',
  },
  teamMembers: 'Ekip Üyeleri',
}

export default translation
