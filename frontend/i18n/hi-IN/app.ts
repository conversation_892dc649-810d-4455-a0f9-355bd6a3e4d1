const translation = {
  createApp: 'ऐप बनाएँ',
  types: {
    all: 'सभी',
    chatbot: 'चैटबॉट',
    agent: 'एजेंट',
    workflow: 'वर्कफ़्लो',
    completion: 'समाप्ति',
    advanced: 'चैटफ्लो',
    basic: 'मूलवर्ती',
  },
  duplicate: 'डुप्लिकेट',
  duplicateTitle: 'ऐप डुप्लिकेट करें',
  export: 'डीएसएल निर्यात करें',
  exportFailed: 'डीएसएल निर्यात विफल हुआ।',
  importDSL: 'डीएसएल फ़ाइल आयात करें',
  createFromConfigFile: 'डीएसएल फ़ाइल से बनाएँ',
  deleteAppConfirmTitle: 'इस ऐप को हटाएँ?',
  deleteAppConfirmContent:
    'ऐप को हटाना अपरिवर्तनीय है। उपयोगकर्ता अब आपके ऐप तक पहुँचने में सक्षम नहीं होंगे, और सभी प्रॉम्प्ट कॉन्फ़िगरेशन और लॉग स्थायी रूप से हटा दिए जाएंगे।',
  appDeleted: 'ऐप हटाया गया',
  appDeleteFailed: 'ऐप हटाने में विफल',
  join: 'समुदाय में शामिल हों',
  communityIntro:
    'टीम के सदस्यों, योगदानकर्ताओं और डेवलपर्स के साथ विभिन्न चैनलों पर चर्चा करें।',
  roadmap: 'हमारा रोडमैप देखें',
  newApp: {
    startFromBlank: 'रिक्त से बनाएँ',
    startFromTemplate: 'टेम्पलेट से बनाएँ',
    captionAppType: 'आप किस प्रकार का ऐप बनाना चाहते हैं?',
    chatbotDescription: 'एक चैट-आधारित एप्लिकेशन बनाएं। यह ऐप प्रश्न-उत्तर प्रारूप का उपयोग करता है, जिससे निरंतर बातचीत के कई राउंड संभव होते हैं।',
    completionDescription: 'ऐसा एप्लिकेशन बनाएं जो प्रॉम्प्ट्स के आधार पर उच्च गुणवत्ता वाला टेक्स्ट उत्पन्न करता है, जैसे लेख, सारांश, अनुवाद आदि उत्पन्न करना।',
    completionWarning: 'इस प्रकार के ऐप का समर्थन नहीं किया जाएगा।',
    agentDescription: 'एक बुद्धिमान एजेंट बनाएं जो स्वायत्त रूप से टूल्स का चयन करके कार्य पूरा कर सके।',
    workflowDescription: 'एक एप्लिकेशन बनाएं जो वर्कफ़्लो ऑर्केस्ट्रेट्स के साथ उच्च डिग्री के कस्टमाइज़ेशन के साथ उच्च गुणवत्ता वाला टेक्स्ट उत्पन्न करता है। यह अनुभवी उपयोगकर्ताओं के लिए उपयुक्त है।',
    workflowWarning: 'वर्तमान में बीटा में',
    chatbotType: 'चैटबॉट ऑर्केस्ट्रेट विधि',
    basic: 'बेसिक',
    basicTip: 'शुरुआती लोगों के लिए, बाद में चैटफ़्लो में स्विच कर सकते हैं',
    basicFor: 'शुरुआती लोगों के लिए',
    basicDescription: 'बेसिक ऑर्केस्ट्रेट चैटबॉट ऐप को सरल सेटिंग्स का उपयोग करके ऑर्केस्ट्रेट करने की अनुमति देता है, बिना अंतर्निहित प्रॉम्प्ट्स को संशोधित करने की क्षमता के। यह शुरुआती लोगों के लिए उपयुक्त है।',
    advanced: 'चैटफ्लो',
    advancedFor: 'अनुभवी उपयोगकर्ताओं के लिए',
    advancedDescription: 'वर्कफ़्लो ऑर्केस्ट्रेट वर्कफ़्लोज़ के रूप में चैटबॉट्स को ऑर्केस्ट्रेट करता है, जिसमें अंतर्निहित प्रॉम्प्ट्स को संपादित करने की क्षमता सहित उच्च डिग्री का कस्टमाइज़ेशन होता है। यह अनुभवी उपयोगकर्ताओं के लिए उपयुक्त है।',
    captionName: 'ऐप आइकन और नाम',
    appNamePlaceholder: 'अपने ऐप को नाम दें',
    captionDescription: 'विवरण',
    appDescriptionPlaceholder: 'ऐप का विवरण दर्ज करें',
    useTemplate: 'इस टेम्पलेट का उपयोग करें',
    previewDemo: 'पूर्वावलोकन डेमो',
    chatApp: 'सहायक',
    chatAppIntro:
      'मैं एक चैट-आधारित एप्लिकेशन बनाना चाहता हूँ। यह ऐप प्रश्न-उत्तर प्रारूप का उपयोग करता है, जिससे निरंतर बातचीत के कई राउंड संभव होते हैं।',
    agentAssistant: 'नया एजेंट सहायक',
    completeApp: 'टेक्स्ट जनरेटर',
    completeAppIntro:
      'मैं एक ऐसा एप्लिकेशन बनाना चाहता हूँ जो प्रॉम्प्ट्स के आधार पर उच्च गुणवत्ता वाला टेक्स्ट उत्पन्न करता है, जैसे लेख, सारांश, अनुवाद आदि उत्पन्न करना।',
    showTemplates: 'मैं टेम्पलेट से चुनना चाहता हूँ',
    hideTemplates: 'मोड चयन पर वापस जाएँ',
    Create: 'बनाएँ',
    Cancel: 'रद्द करें',
    nameNotEmpty: 'नाम खाली नहीं हो सकता',
    appTemplateNotSelected: 'कृपया एक टेम्पलेट चुनें',
    appTypeRequired: 'कृपया एक ऐप प्रकार चुनें',
    appCreated: 'ऐप बनाया गया',
    appCreateFailed: 'ऐप बनाने में विफल',
    Confirm: 'सुदृढ़ करना',
    appCreateDSLErrorPart4: 'सिस्टम-समर्थित DSL संस्करण:',
    appCreateDSLErrorPart3: 'वर्तमान अनुप्रयोग डीएसएल संस्करण:',
    caution: 'सावधानी',
    appCreateDSLErrorTitle: 'संस्करण असंगति',
    appCreateDSLErrorPart1: 'डीएसएल संस्करणों में एक महत्वपूर्ण अंतर पाया गया है। आयात को बाध्य करने से अनुप्रयोग में खराबी आ सकती है।',
    appCreateDSLWarning: 'सावधानी: DSL संस्करण अंतर कुछ सुविधाओं को प्रभावित कर सकता है',
    appCreateDSLErrorPart2: 'क्या आप जारी रखना चाहते हैं?',
    learnMore: 'और जानो',
    forBeginners: 'नए उपयोगकर्ताओं के लिए बुनियादी ऐप प्रकार',
    foundResults: '{{count}} परिणाम',
    forAdvanced: 'उन्नत उपयोगकर्ताओं के लिए',
    agentUserDescription: 'पुनरावृत्त तर्क और स्वायत्त उपकरण में सक्षम एक बुद्धिमान एजेंट कार्य लक्ष्यों को प्राप्त करने के लिए उपयोग करता है।',
    optional: 'वैकल्पिक',
    chatbotShortDescription: 'सरल सेटअप के साथ एलएलएम-आधारित चैटबॉट',
    foundResult: '{{count}} परिणाम',
    completionUserDescription: 'सरल कॉन्फ़िगरेशन के साथ पाठ निर्माण कार्यों के लिए त्वरित रूप से AI सहायक बनाएं।',
    noIdeaTip: 'कोई विचार नहीं? हमारे टेम्प्लेट देखें',
    noTemplateFound: 'कोई टेम्पलेट नहीं मिला',
    completionShortDescription: 'पाठ निर्माण कार्यों के लिए AI सहायक',
    noAppsFound: 'कोई ऐप्लिकेशन नहीं मिला',
    chooseAppType: 'ऐप प्रकार चुनें',
    agentShortDescription: 'तर्क और स्वायत्त उपकरण उपयोग के साथ बुद्धिमान एजेंट',
    workflowShortDescription: 'बुद्धिमान स्वचालन के लिए एजेंटिक प्रवाह',
    chatbotUserDescription: 'सरल कॉन्फ़िगरेशन के साथ जल्दी से एलएलएम-आधारित चैटबॉट बनाएं। आप बाद में चैटफ्लो पर स्विच कर सकते हैं।',
    advancedUserDescription: 'अतिरिक्त मेमोरी सुविधाओं और चैटबॉट इंटरफेस के साथ वर्कफ़्लो।',
    advancedShortDescription: 'बहु-चरण वार्तालाप के लिए उन्नत वर्कफ़्लो',
    noTemplateFoundTip: 'विभिन्न कीवर्ड का उपयोग करके खोजने का प्रयास करें।',
    workflowUserDescription: 'ड्रैग-एंड-ड्रॉप सरलता के साथ स्वायत्त AI वर्कफ़्लो का दृश्य निर्माण करें।',
    dropDSLToCreateApp: 'यहाँ DSL फ़ाइल ड्रॉप करें ताकि ऐप बनाया जा सके',
  },
  editApp: 'जानकारी संपादित करें',
  editAppTitle: 'ऐप जानकारी संपादित करें',
  editDone: 'ऐप जानकारी अपडेट की गई',
  editFailed: 'ऐप जानकारी अपडेट करने में विफल',
  iconPicker: {
    ok: 'ठीक है',
    cancel: 'रद्द करें',
    emoji: 'इमोजी',
    image: 'छवि',
  },
  switch: 'वर्कफ़्लो ऑर्केस्ट्रेट पर स्विच करें',
  switchTipStart: 'आपके लिए एक नई ऐप कॉपी बनाई जाएगी, और नई कॉपी वर्कफ़्लो ऑर्केस्ट्रेट में स्विच हो जाएगी। नई कॉपी ',
  switchTip: 'की अनुमति नहीं देगा',
  switchTipEnd: ' बेसिक ऑर्केस्ट्रेट में स्विच करना।',
  switchLabel: 'बनाई जाने वाली ऐप कॉपी',
  removeOriginal: 'मूल ऐप हटाएँ',
  switchStart: 'स्विच शुरू करें',
  typeSelector: {
    all: 'सभी प्रकार',
    chatbot: 'चैटबॉट',
    agent: 'एजेंट',
    workflow: 'वर्कफ़्लो',
    completion: 'समाप्ति',
    advanced: 'चैटफ्लो',
  },
  tracing: {
    title: 'एप्लिकेशन प्रदर्शन ट्रेसिंग',
    description: 'तृतीय-पक्ष LLMOps प्रदाता को कॉन्फ़िगर करना और एप्लिकेशन प्रदर्शन का ट्रेस करना।',
    config: 'कॉन्फ़िगर करें',
    collapse: 'संकुचित करें',
    expand: 'विस्तृत करें',
    tracing: 'ट्रेसिंग',
    disabled: 'अक्षम',
    disabledTip: 'कृपया पहले प्रदाता को कॉन्फ़िगर करें',
    enabled: 'सेवा में',
    tracingDescription: 'एप्लिकेशन निष्पादन का पूरा संदर्भ कैप्चर करें, जिसमें LLM कॉल, संदर्भ, प्रॉम्प्ट्स, HTTP अनुरोध और अधिक शामिल हैं, एक तृतीय-पक्ष ट्रेसिंग प्लेटफ़ॉर्म पर।',
    configProviderTitle: {
      configured: 'कॉन्फ़िगर किया गया',
      notConfigured: 'ट्रेसिंग सक्षम करने के लिए प्रदाता कॉन्फ़िगर करें',
      moreProvider: 'अधिक प्रदाता',
    },
    arize: {
      title: 'Arize',
      description: 'एंटरप्राइज-स्तरीय LLM ऑब्ज़र्वेबिलिटी, ऑनलाइन और ऑफ़लाइन मूल्यांकन, मॉनिटरिंग और प्रयोग — OpenTelemetry द्वारा समर्थित। LLM और एजेंट-आधारित अनुप्रयोगों के लिए विशेष रूप से तैयार किया गया।',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'आपके LLM वर्कफ़्लोज़ और एजेंट्स के लिए ओपन-सोर्स और OpenTelemetry-आधारित ऑब्ज़र्वेबिलिटी, मूल्यांकन, प्रॉम्प्ट इंजीनियरिंग और प्रयोग का प्लेटफ़ॉर्म।',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'LLM-संचालित एप्लिकेशन जीवनचक्र के प्रत्येक चरण के लिए एक ऑल-इन-वन डेवलपर प्लेटफ़ॉर्म।',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'आपके LLM एप्लिकेशन को डीबग और सुधारने के लिए ट्रेस, मूल्यांकन, प्रॉम्प्ट प्रबंधन और मेट्रिक्स।',
    },
    inUse: 'उपयोग में',
    configProvider: {
      title: 'कॉन्फ़िगर करें ',
      placeholder: 'अपना {{key}} दर्ज करें',
      project: 'प्रोजेक्ट',
      publicKey: 'सार्वजनिक कुंजी',
      secretKey: 'गुप्त कुंजी',
      viewDocsLink: '{{key}} दस्तावेज़ देखें',
      removeConfirmTitle: '{{key}} कॉन्फ़िगरेशन हटाएं?',
      removeConfirmContent: 'वर्तमान कॉन्फ़िगरेशन उपयोग में है, इसे हटाने से ट्रेसिंग सुविधा बंद हो जाएगी।',
    },
    view: 'देखना',
    opik: {
      title: 'ओपिक',
      description: 'ओपिक एलएलएम अनुप्रयोगों के मूल्यांकन, परीक्षण और निगरानी के लिए एक ओपन-सोर्स प्लेटफॉर्म है।',
    },
    weave: {
      title: 'बुनना',
      description: 'वीव एक ओपन-सोर्स प्लेटफ़ॉर्म है जो LLM अनुप्रयोगों का मूल्यांकन, परीक्षण और निगरानी करने के लिए है।',
    },
    aliyun: {
      title: 'क्लाउड मॉनिटर',
      description: 'अलीबाबा क्लाउड द्वारा प्रदान की गई पूरी तरह से प्रबंधित और रखरखाव-मुक्त अवलोकन प्लेटफ़ॉर्म, Dify अनुप्रयोगों की स्वचालित निगरानी, ट्रेसिंग और मूल्यांकन का सक्षम बनाता है।',
    },
  },
  answerIcon: {
    title: 'बदलने 🤖 के लिए web app चिह्न का उपयोग करें',
    descriptionInExplore: 'एक्सप्लोर में बदलने 🤖 के लिए वेबऐप आइकन का उपयोग करना है या नहीं',
    description: 'साझा अनुप्रयोग में प्रतिस्थापित 🤖 करने के लिए web app चिह्न का उपयोग करना है या नहीं',
  },
  importFromDSLFile: 'डीएसएल फ़ाइल से',
  importFromDSLUrl: 'यूआरएल से',
  importFromDSL: 'DSL से आयात करें',
  importFromDSLUrlPlaceholder: 'डीएसएल लिंक यहां पेस्ट करें',
  mermaid: {
    handDrawn: 'हाथ खींचा',
    classic: 'क्लासिक',
  },
  openInExplore: 'एक्सप्लोर में खोलें',
  newAppFromTemplate: {
    sidebar: {
      Writing: 'कृतियाँ',
      Recommended: 'अनुशंसित',
      Workflow: 'कार्यप्रवाह',
      Assistant: 'बिक्री सहायक',
      Agent: 'आढ़तिया',
      Programming: 'प्रोग्रामिंग',
      HR: 'घंटा',
    },
    byCategories: 'श्रेणियों द्वारा',
    searchAllTemplate: 'सभी टेम्पलेट्स खोजें...',
  },
  showMyCreatedAppsOnly: 'केवल मेरे बनाए गए ऐप्स दिखाएं',
  appSelector: {
    params: 'ऐप पैरामीटर',
    noParams: 'कोई पैरामीटर की आवश्यकता नहीं है।',
    placeholder: 'एक ऐप चुनें...',
    label: 'ऐप',
  },
  structOutput: {
    structured: 'संरचित',
    required: 'आवश्यक',
    LLMResponse: 'LLM प्रतिक्रिया',
    moreFillTip: 'अधिकतम 10 स्तरों की नेस्टिंग दिखाना',
    modelNotSupported: 'मॉडल का समर्थन नहीं किया गया',
    configure: 'कॉन्फ़िगर करें',
    notConfiguredTip: 'संरचित आउटपुट को अभी तक कॉन्फ़िगर नहीं किया गया है',
    structuredTip: 'संरचित आउटपुट एक विशेषता है जो यह सुनिश्चित करती है कि मॉडल हमेशा आपके प्रदान किए गए JSON स्कीमा के अनुसार प्रतिक्रियाएँ生成 करेगा।',
    modelNotSupportedTip: 'वर्तमान मॉडल इस सुविधा का समर्थन नहीं करता है और स्वचालित रूप से प्रॉम्प्ट इंजेक्शन में डाउनग्रेड किया जाता है।',
  },
  accessItemsDescription: {
    anyone: 'कोई भी वेब ऐप तक पहुँच सकता है',
    organization: 'संस्थान के किसी भी व्यक्ति को वेब ऐप तक पहुंच प्राप्त है',
    specific: 'केवल विशेष समूह या सदस्य ही वेब ऐप तक पहुंच सकते हैं',
    external: 'केवल प्रमाणित बाहरी उपयोगकर्ता वेब अनुप्रयोग तक पहुँच सकते हैं',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'लिंक के साथ कोई भी',
      specific: 'विशिष्ट समूह या सदस्य',
      organization: 'केवल उद्यम के भीतर के सदस्य',
      external: 'प्रमाणित बाहरी उपयोगकर्ता',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'समूहों और सदस्यों की खोज करें',
      allMembers: 'सभी सदस्य',
      expand: 'व्याप्त करें',
      noResult: 'कोई परिणाम नहीं',
    },
    title: 'वेब एप्लिकेशन पहुँच नियंत्रण',
    description: 'वेब ऐप एक्सेस अनुमतियाँ सेट करें',
    groups_one: '{{count}} समूह',
    groups_other: '{{count}} समूह',
    members_one: '{{count}} सदस्य',
    members_other: '{{count}} सदस्य',
    noGroupsOrMembers: 'कोई समूह या सदस्य चयनित नहीं किया गया',
    updateSuccess: 'सफलता से अपडेट किया गया',
    accessLabel: 'किसके पास पहुँच है',
    webAppSSONotEnabledTip: 'कृपया वेब ऐप प्रमाणीकरण विधि कॉन्फ़िगर करने के लिए उद्यम प्रशासक से संपर्क करें।',
  },
  publishApp: {
    title: 'वेब ऐप तक कौन पहुँच सकता है',
    notSet: 'अनुबंधित नहीं किया गया',
    notSetDesc: 'वर्तमान में कोई भी वेब ऐप तक पहुंच नहीं बना सकता। कृपया अनुमतियाँ सेट करें।',
  },
  accessControl: 'वेब एप्लिकेशन पहुँच नियंत्रण',
  noAccessPermission: 'वेब एप्लिकेशन तक पहुँचने की अनुमति नहीं है',
  maxActiveRequests: 'अधिकतम समवर्ती अनुरोध',
  maxActiveRequestsPlaceholder: 'असीमित के लिए 0 दर्ज करें',
}

export default translation
