const translation = {
  title: '<PERSON><PERSON><PERSON>',
  name: '<PERSON><PERSON><PERSON><PERSON> na opombo',
  editBy: '<PERSON><PERSON><PERSON><PERSON> je uredil {{author}}',
  noData: {
    title: '<PERSON><PERSON> opomb',
    description: 'Opombe lahko urejate med odpravljanjem napak v aplikaciji ali jih množično uvozite tukaj za visokokakovosten odgovor.',
  },
  table: {
    header: {
      question: 'vprašanje',
      answer: 'odgovor',
      createdAt: 'ustvarjeno ob',
      hits: 'zadetki',
      actions: 'dejanja',
      addAnnotation: 'Dodaj opombo',
      bulkImport: 'M<PERSON>žični uvoz',
      bulkExport: 'Množični izvoz',
      clearAll: 'Počisti vse opombe',
    },
  },
  editModal: {
    title: 'Uredi odgovor na opombo',
    queryName: 'Uporab<PERSON>š<PERSON> vprašanje',
    answerName: '<PERSON>ripovedovale<PERSON> Bot',
    yourAnswer: '<PERSON><PERSON><PERSON> odgo<PERSON>',
    answerPlaceholder: 'Vnesite svoj odgovor tukaj',
    yourQuery: 'Vaše vprašanje',
    queryPlaceholder: 'Vnesite svoje vprašanje tukaj',
    removeThisCache: 'Odstrani to opombo',
    createdAt: 'Ustvarjeno ob',
  },
  addModal: {
    title: 'Dodaj odgovor na opombo',
    queryName: 'Vprašanje',
    answerName: 'Odgovor',
    answerPlaceholder: 'Vnesite odgovor tukaj',
    queryPlaceholder: 'Vnesite vprašanje tukaj',
    createNext: 'Dodaj še en odgovor z opombo',
  },
  batchModal: {
    title: 'Množični uvoz',
    csvUploadTitle: 'Povlecite in spustite svoj CSV datoteko tukaj ali ',
    browse: 'poiščite',
    tip: 'CSV datoteka mora ustrezati naslednji strukturi:',
    question: 'vprašanje',
    answer: 'odgovor',
    contentTitle: 'vsebina fragmenta',
    content: 'vsebina',
    template: 'Prenesite predlogo tukaj',
    cancel: 'Prekliči',
    run: 'Zaženi množično obdelavo',
    runError: 'Napaka pri množičnem zagonu',
    processing: 'V množični obdelavi',
    completed: 'Uvoz zaključen',
    error: 'Napaka pri uvozu',
    ok: 'V redu',
  },
  errorMessage: {
    answerRequired: 'Odgovor je obvezen',
    queryRequired: 'Vprašanje je obvezno',
  },
  viewModal: {
    annotatedResponse: 'Odgovor na opombo',
    hitHistory: 'Zgodovina zadetkov',
    hit: 'Zadetek',
    hits: 'Zadetki',
    noHitHistory: 'Ni zgodovine zadetkov',
  },
  hitHistoryTable: {
    query: 'Vprašanje',
    match: 'Ujemanje',
    response: 'Odgovor',
    source: 'Vir',
    score: 'Rezultat',
    time: 'Čas',
  },
  initSetup: {
    title: 'Začetna nastavitev odgovora na opombo',
    configTitle: 'Nastavitev odgovora na opombo',
    confirmBtn: 'Shrani in omogoči',
    configConfirmBtn: 'Shrani',
  },
  embeddingModelSwitchTip: 'Model za vektorizacijo besedila opomb, preklapljanje modelov bo ponovno vektoriziralo, kar bo povzročilo dodatne stroške.',
}

export default translation
