# 设置 PowerShell 控制台编码
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8
$PSDefaultParameterValues['*:Encoding'] = 'utf8'
chcp 65001 > $null

Write-Host "================================" -ForegroundColor Green
Write-Host "Dify Web 构建打包脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 清理旧文件
Write-Host "清理旧文件..." -ForegroundColor Yellow
@(".next", "deploy-package", "frontend.tgz") | ForEach-Object {
    if (Test-Path $_) { Remove-Item -Path $_ -Recurse -Force }
}

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
pnpm install
if ($LASTEXITCODE -ne 0) { Write-Host "依赖安装失败!" -ForegroundColor Red; exit 1 }

# 构建项目
Write-Host "构建项目..." -ForegroundColor Yellow
pnpm run build
if ($LASTEXITCODE -ne 0) { Write-Host "项目构建失败!" -ForegroundColor Red; exit 1 }

# 准备部署包
Write-Host "准备部署包..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "deploy-package" | Out-Null
Copy-Item -Path ".next/standalone/*" -Destination "deploy-package/" -Recurse -Force
New-Item -ItemType Directory -Force -Path "deploy-package/.next" | Out-Null
Copy-Item -Path ".next/static" -Destination "deploy-package/.next/" -Recurse -Force
if (Test-Path "public") { Copy-Item -Path "public" -Destination "deploy-package/" -Recurse -Force }

# 压缩打包
Write-Host "压缩打包..." -ForegroundColor Yellow
tar -czf frontend.tgz -C deploy-package .
if ($LASTEXITCODE -ne 0) { Write-Host "压缩失败!" -ForegroundColor Red; exit 1 }

# 清理临时文件
Remove-Item -Path "deploy-package" -Recurse -Force

# 显示结果
$file = Get-Item "frontend.tgz"
Write-Host "================================" -ForegroundColor Green
Write-Host "✅ 打包完成!" -ForegroundColor Green
Write-Host ("📦 文件: " + $file.Name) -ForegroundColor Cyan
Write-Host ("📏 大小: " + [math]::Round($file.Length / 1MB, 2) + " MB") -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Green
